using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Aspose.Words;
using Aspose.Words.Saving;

namespace DocumentToPdfConverter
{
    /// <summary>
    /// 文档转换器类：将文件夹中的所有支持文件转换为PDF
    /// </summary>
    public class DocumentConverter
    {
        // 支持的文件扩展名
        private static readonly string[] SupportedExtensions = new[]
        {
            ".doc", ".docx", ".rtf", ".txt", ".odt", ".ott", ".bmp", ".gif", 
            ".jpeg", ".jpg", ".png", ".tiff", ".tif", ".emf", ".wmf", ".html", ".mhtml", ".xml"
        };

        /// <summary>
        /// 将源文件夹中的所有支持的文件转换为PDF格式
        /// </summary>
        /// <param name="sourceFolderPath">源文件夹路径</param>
        /// <returns>转换是否成功</returns>
        public async Task<bool> ConvertFolderToPdfAsync(string sourceFolderPath)
        {
            try
            {
                if (!Directory.Exists(sourceFolderPath))
                {
                    return false;
                }

                // 创建目标文件夹 - 源文件夹名称+[PDF]
                string sourceParentPath = Directory.GetParent(sourceFolderPath).FullName;
                string sourceFolderName = new DirectoryInfo(sourceFolderPath).Name;
                string destinationRootPath = Path.Combine(sourceParentPath, $"{sourceFolderName}【PDF】");

                // 确保目标根文件夹存在
                if (!Directory.Exists(destinationRootPath))
                {
                    Directory.CreateDirectory(destinationRootPath);
                }

                // 获取所有需要处理的文件
                List<string> filesToProcess = GetAllSupportedFiles(sourceFolderPath);

                // 并行处理文件
                await Task.Run(() =>
                {
                    Parallel.ForEach(filesToProcess, file =>
                    {
                        try
                        {
                            // 计算目标路径
                            string relativePath = file.Substring(sourceFolderPath.Length);
                            string destinationFilePath = Path.Combine(destinationRootPath, 
                                Path.ChangeExtension(relativePath, ".pdf").TrimStart('\\', '/'));

                            // 确保目标文件夹存在
                            string destinationFolder = Path.GetDirectoryName(destinationFilePath);
                            if (!Directory.Exists(destinationFolder))
                            {
                                Directory.CreateDirectory(destinationFolder);
                            }

                            // 转换文件为PDF
                            ConvertToPdf(file, destinationFilePath);
                        }
                        catch
                        {
                            // 忽略单个文件的错误，继续处理其他文件
                        }
                    });
                });

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取文件夹中所有支持的文件
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        /// <returns>支持的文件路径列表</returns>
        private List<string> GetAllSupportedFiles(string folderPath)
        {
            List<string> result = new List<string>();

            try
            {
                // 获取当前文件夹中所有支持的文件
                foreach (string extension in SupportedExtensions)
                {
                    result.AddRange(Directory.GetFiles(folderPath, $"*{extension}", SearchOption.TopDirectoryOnly));
                }

                // 递归处理所有子文件夹
                foreach (string subFolder in Directory.GetDirectories(folderPath))
                {
                    result.AddRange(GetAllSupportedFiles(subFolder));
                }
            }
            catch
            {
                // 忽略获取文件列表中的错误
            }

            return result;
        }

        /// <summary>
        /// 将单个文件转换为PDF
        /// </summary>
        /// <param name="sourceFilePath">源文件路径</param>
        /// <param name="destinationFilePath">目标文件路径</param>
        private void ConvertToPdf(string sourceFilePath, string destinationFilePath)
        {
            try
            {
                // 加载文档
                Document doc = new Document(sourceFilePath);

                // 设置PDF保存选项
                PdfSaveOptions saveOptions = new PdfSaveOptions
                {
                    Compliance = PdfCompliance.PdfA1b,
                    SaveFormat = SaveFormat.Pdf
                };

                // 保存为PDF
                doc.Save(destinationFilePath, saveOptions);
            }
            catch
            {
                // 忽略单个文件的转换错误
            }
        }
    }
}