using System.Security.Cryptography;

namespace WordProcessorLib.Services;

public class BackupService
{
    // public string BackupFolderPath { get; private set; } = string.Empty;

    // ======================================== 备份服务核心逻辑 ========================================
    // 功能：将 sourceFolderPath 文件夹及其子文件夹里的所有文件，复制到 backupFolderPath，并保持目录结构一致
    // 特点：提供高可靠性文件备份服务，包含并行复制、完整性校验、自动重试等机制
    // 设计要点：
    // 1. 多线程并行复制：利用 Parallel.ForEach 实现文件级并行处理，MaxDegreeOfParallelism 设置为 CPU核心数-1
    // 2. 三级容错机制：
    //    - 文件复制重试（最大3次）
    //    - 文件内容SHA256校验
    //    - 失败时自动清理残留
    // 3. 原子性保证：任何一个文件失败时回滚整个备份操作
    // sourceFolderPath：需要备份的源文件夹路径，必须是有效的本地目录
    // BackupFolderPath：备份成功后生成的备份目录路径，格式为"源路径_备份_时间戳"
    public bool CreateBackup(string sourceFolderPath, string backupFolderPath)
    {
        try
        {
            // ================== 复制目录结构（含空目录） ==================
            // 作用：确保源目录的空子目录结构完全保留
            // 设计要点：
            //   - 使用独立于文件复制的目录遍历逻辑
            //   - 先于文件复制执行，避免并行操作干扰目录创建
            CopyDirectoryStructure(sourceFolderPath, backupFolderPath);
            
            // ================================== 并行文件复制 ==================================
            // 使用 Parallel.ForEach 实现多线程加速：
            // - 遍历源目录下所有文件（含子目录）
            //   保留1个CPU核心给主线程和其他操作
            // - 每个文件通过 CopyFileWithRetry 方法处理
            var sourceFilePaths = Directory.EnumerateFiles(sourceFolderPath,
                                            "*", SearchOption.AllDirectories);
            var parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = Math.Max(1, Environment.ProcessorCount - 1)
            };
            Parallel.ForEach(sourceFilePaths, parallelOptions,sourceFilePath =>
                             CopyFileWithRetry(sourceFilePath, sourceFolderPath, backupFolderPath));
            return true;
        }
        catch
        {
            // ============ 异常时清理残留 ============
            // 触发条件：任何未处理异常（包括文件校验失败）
            // 删除已创建的备份目录，保持系统状态干净
            CleanupFailedBackup(backupFolderPath);
            return false;
        }
    }
    
    // ============================== 复制目录结构（含空目录） ==============================
    // 作用：递归复制源目录所有子目录结构（包括空目录）到备份目录
    // 参数：
    //   sourceFolderPath - 源目录根路径（示例：/src）
    //   backupFolderPath - 备份目录根路径（示例：/src_备份_时间戳）
    // 实现要点：
    //   - 使用广度优先遍历保证父目录先于子目录创建
    //   - 依赖 Directory.CreateDirectory 的幂等性保证线程安全
    private void CopyDirectoryStructure(string sourceFolderPath, string backupFolderPath)
    {
        // 遍历源目录下所有子目录（含嵌套）
        var allDirectories = Directory.EnumerateDirectories(sourceFolderPath,
                                                "*", SearchOption.AllDirectories);
        foreach (var dir in allDirectories)
        {
            // 计算相对路径（示例：将/src/sub转换为sub）
            var relativePath = Path.GetRelativePath(sourceFolderPath, dir);
            
            // 构建目标目录路径（示例：/backup/sub）
            var destDir = Path.Combine(backupFolderPath, relativePath);
            
            // 创建目录（幂等操作，已存在时自动跳过）
            Directory.CreateDirectory(destDir);
        }
    }
    
    // ============================== 带重试机制的文件复制 ==============================
    // 作用：实现带重试和校验的文件复制操作
    // 参数：
    //   sourceFile - 源文件绝对路径（示例：/src/a.txt）
    //   sourceFolderPath - 源文件夹路径（用于计算相对路径）
    // 重试策略：
    //   - 最大重试次数3次（兼顾效率与容错）
    //   - 每次重试间隔100ms（避免瞬时错误）
    //   - 最终失败时抛出异常终止整个备份
    private void CopyFileWithRetry(string sourceFilePath, string sourceFolderPath, string backupFolderPath)
    {
        const int maxRetries = 3;
        for (int i = 0; i < maxRetries; i++)
        {
            try
            {
                // ============================= 计算目标路径 =============================
                // 保留原始目录结构（示例：将/src/sub/a.txt备份到/backup/sub/a.txt）
                // Path.GetRelativePath的作用是获取两个路径之间的相对路径。
                // 第一个参数是基路径，第二个参数是目标路径，返回的是从基路径到目标路径的相对路径。
                // 例如，如果基路径是“C:/FolderA”，目标路径是“C:/FolderA/SubFolder/file.txt”，
                // 那么返回的就是“SubFolder/file.txt”。这一步是为了在备份时保持目录结构。
                var relativePath = Path.GetRelativePath(sourceFolderPath, sourceFilePath);
                // Path.Combine 这个方法用于将多个路径片段合并成一个完整的路径。
                // 它自动处理路径分隔符，避免手动拼接可能出现的错误，比如多余的分隔符或缺失的分隔符。
                var destFilePath = Path.Combine(backupFolderPath, relativePath);
                
                // ======================= 创建目录结构 =======================
                // 自动递归创建所有不存在的目录层级，确保目标目录存在
                Directory.CreateDirectory(Path.GetDirectoryName(destFilePath)!);
                
                // =============== 执行文件复制 ===============
                // 使用覆盖模式（overwrite=true）处理已存在文件
                File.Copy(sourceFilePath, destFilePath, true);
                
                // ========================== 即时校验 ==========================
                // 复制完成后立即校验文件完整性
                if (!VerifyFileHash(sourceFilePath, destFilePath))
                    throw new InvalidDataException($"文件校验失败: {sourceFilePath}");
                return;
            }
            catch (IOException) when (i < maxRetries - 1)
            {
                // 捕获IO异常时进行重试（最后一次重试不捕获）
                Thread.Sleep(100);
            }
        }
    }

    // ================================= 文件哈希校验 =================================
    // 作用：验证源文件和备份文件的二进制一致性
    // 算法选择：
    //   SHA256算法（碰撞概率1/2^128，远高于MD5的安全性）
    // 实现要点：
    //   - 使用using自动释放资源（SHA实例、文件流）
    //   - 顺序比较字节数组的每个元素
    private bool VerifyFileHash(string source, string backup)
    {
        using var sha = SHA256.Create();
        using var srcStream = File.OpenRead(source);
        using var bakStream = File.OpenRead(backup);
        return sha.ComputeHash(srcStream).SequenceEqual(sha.ComputeHash(bakStream));
    }

    // ======================================== 单文件备份服务核心逻辑 ========================================
    // 功能：将单个文件备份到指定路径，并提供完整性校验
    // 特点：提供高可靠性单文件备份服务，包含重试机制、完整性校验等
    // 设计要点：
    // 1. 三次重试机制：文件复制失败时自动重试，最大重试次数3次
    // 2. SHA256校验：确保备份文件与源文件完全一致
    // 3. 原子性保证：备份失败时自动清理残留文件
    // sourceFilePath：需要备份的源文件路径，必须是有效的本地文件
    // backupFilePath：备份文件的完整路径，包含文件名
    public bool CreateSingleFileBackup(string sourceFilePath, string backupFilePath)
    {
        try
        {
            // ========================== 创建备份文件所在目录 ==========================
            // 确保备份文件的目录结构存在
            var backupDir = Path.GetDirectoryName(backupFilePath);
            if (!string.IsNullOrEmpty(backupDir))
                Directory.CreateDirectory(backupDir);
            
            // ============================== 带重试机制的文件复制 ==============================
            const int maxRetries = 3;
            for (int i = 0; i < maxRetries; i++)
            {
                try
                {
                    // =============== 执行文件复制 ===============
                    // 使用覆盖模式（overwrite=true）处理已存在文件
                    File.Copy(sourceFilePath, backupFilePath, true);
                    
                    // ========================== 即时校验 ==========================
                    // 复制完成后立即校验文件完整性
                    if (!VerifyFileHash(sourceFilePath, backupFilePath))
                        throw new InvalidDataException($"文件校验失败: {sourceFilePath}");
                    
                    return true; // 成功完成备份
                }
                catch (IOException) when (i < maxRetries - 1)
                {
                    // 捕获IO异常时进行重试（最后一次重试不捕获）
                    Thread.Sleep(100);
                }
            }
            return false; // 重试次数用尽，备份失败
        }
        catch
        {
            // ============ 异常时清理残留文件 ============
            CleanupFailedSingleFileBackup(backupFilePath);
            return false;
        }
    }

    // ======================= 备份失败清理（文件夹版本） =======================
    // 作用：删除不完整的备份目录
    // 触发场景：
    //   - 文件复制失败
    //   - 文件校验失败
    //   - 未知异常发生
    // 安全机制：
    //   - 检查目录存在性后再执行删除
    //   - 递归删除所有子目录和文件
    private void CleanupFailedBackup(string backupFolderPath)
    {
        if (Directory.Exists(backupFolderPath))
            Directory.Delete(backupFolderPath, recursive: true);
    }
    
    // ======================= 备份失败清理（单文件版本） =======================
    // 作用：删除不完整的备份文件
    // 触发场景：
    //   - 单文件复制失败
    //   - 单文件校验失败
    //   - 未知异常发生
    // 安全机制：
    //   - 检查文件存在性后再执行删除
    private void CleanupFailedSingleFileBackup(string backupFilePath)
    {
        if (File.Exists(backupFilePath))
            File.Delete(backupFilePath);
    }
}