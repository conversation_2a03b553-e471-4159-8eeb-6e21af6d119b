namespace WordProcessorLib.Services;
public static class RollbackService
// 定义静态类（不可实例化，直接通过类名调用方法）
{
    // ======================================== 智能回滚执行入口 ========================================
    // 作用：根据源路径类型（文件夹或文件）自动选择相应的回滚策略
    // 参数：sourcePath（被处理的原始路径，可以是文件夹或文件）, backupPath（备份路径）
    // 文件夹回滚：将源文件夹删除，然后将备份文件夹重命名为源文件夹名
    // 文件回滚：将处理后的文件删除，然后将备份文件重命名为源文件名
    public static void Execute(string sourcePath, string backupPath)
    {
        // ========================== 检测源路径类型并选择回滚策略 ==========================
        // 优先检测原始源路径，如果不存在则检测备份路径来推断类型
        bool isDirectory;
        if (Directory.Exists(sourcePath))
        {
            isDirectory = true;
        }
        else if (File.Exists(sourcePath))
        {
            isDirectory = false;
        }
        else if (Directory.Exists(backupPath))
        {
            isDirectory = true;
        }
        else if (File.Exists(backupPath))
        {
            isDirectory = false;
        }
        else
        {
            Console.WriteLine("错误：无法确定源路径类型，源文件/文件夹和备份都不存在！");
            return;
        }

        // ============================= 根据类型执行相应的回滚逻辑 =============================
        if (isDirectory)
        {
            ExecuteFolderRollback(sourcePath, backupPath);
        }
        else
        {
            ExecuteFileRollback(sourcePath, backupPath);
        }
    }
    
    // ======================================== 文件夹回滚逻辑 ========================================
    // 作用：将 sourceFolderPath 文件夹删除，然后将 backupFolderPath 的名字改成 sourceFolderPath 的名字
    // 静态方法，参数：sourceFolderPath（被处理的原始路径）, backupFolderPath（备份路径）
    // 处理中会先备份 backupFolderPath，生成 tempBackupFolderPath 文件夹，
    // 如果处理成功则 tempBackupFolderPath 文件夹会自动删除
    private static void ExecuteFolderRollback(string sourceFolderPath, string backupFolderPath)
    {
        try
        {
            // ======================= 删除源文件夹及其所有内容 =======================
            // recursive: true表示递归删除子目录和文件
            try
            {
                if (Directory.Exists(sourceFolderPath))
                    Directory.Delete(sourceFolderPath, recursive: true);
            }
            catch (Exception)
            {
                Console.WriteLine($"源文件夹删除失败！请手动操作: \n" +
                                  $"1.删除源文件夹【{sourceFolderPath}】（如存在）\n" +
                                  $"2.将备份文件夹【{backupFolderPath}】的后缀名删除");
                return;
            }
            
            // =================== 生成带时间戳的临时备份文件夹路径 ===================
            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            var tempBackupFolderPath = $"{sourceFolderPath}_临时备份_{timestamp}";
            
            
            // =============== 显式创建临时备份文件夹 ===============
            // 作用：确保即使源目录为空或仅含空目录时，备份目录仍能被创建
            // 设计要点：先于任何文件操作创建根目录，保证备份标记存在
            Directory.CreateDirectory(tempBackupFolderPath);
            
            // ============================ 创建备份文件夹的临时备份 ============================
            // 执行临时备份操作，通过返回值判断备份成功与否，备份成功继续往下执行，备份失败直接退出程序
            var backupService = new BackupService(); 
            if (!backupService.CreateBackup(backupFolderPath, tempBackupFolderPath)) 
            {
                Console.WriteLine($"备份文件夹的临时备份创建失败！请手动操作: \n" +
                                  $"1.删除临时备份文件夹【{tempBackupFolderPath}】（如存在）\n" +
                                  $"2.将备份文件夹【{backupFolderPath}】的后缀名删除");
                return;
            }
            
            // ======================== 将备份文件夹名称重命名为源文件夹名称 ========================
            // 效果：如果 sourceFolderPath 不存在 → backupFolderPath 会被重命名为 sourceFolderPath
            //      如果 sourceFolderPath 已存在 → 会抛出异常，因此代码中要先删除源文件夹
            try
            {
                Directory.Move(backupFolderPath, sourceFolderPath);
            }
            catch (Exception)
            {
                Console.WriteLine("备份文件夹名称重命名为源文件夹名称操作失败！请手动操作:");
                Console.WriteLine($"1.删除备份文件夹【{backupFolderPath}】（如存在）");
                Console.WriteLine($"2.将临时备份文件夹【{tempBackupFolderPath}】的后缀名删除");
                return;
            }
            
            // ======================= 删除临时备份文件夹及其所有内容 =======================
            // recursive: true表示递归删除子目录和文件
            try
            {
                if (Directory.Exists(tempBackupFolderPath))
                    Directory.Delete(tempBackupFolderPath, recursive: true);
            }
            catch (Exception)
            {
                Console.WriteLine($"临时备份文件夹删除失败！请手动操作: \n" +
                                  $"将临时备份文件夹【{tempBackupFolderPath}】删除（如存在）");
                return;
            }
            Console.WriteLine("回滚成功完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"未知异常：{ex.Message}\n" +
                              $"请检查源文件夹与各备份文件夹之间的差异并保留其中的最完整的一份，" +
                              $"在排除错误后重新运行程序");
        }
    }
    
    // ======================================== 单文件回滚逻辑 ========================================
    // 作用：将处理后的源文件删除，然后将备份文件重命名为源文件名
    // 参数：sourceFilePath（被处理的原始文件路径）, backupFilePath（备份文件路径）
    // 设计要点：
    // 1. 单文件回滚逻辑更简单，不需要临时备份
    // 2. 直接删除源文件，然后重命名备份文件
    // 3. 提供详细的错误处理和用户指导
    private static void ExecuteFileRollback(string sourceFilePath, string backupFilePath)
    {
        try
        {
            // ======================= 删除处理后的源文件 =======================
            try
            {
                if (File.Exists(sourceFilePath))
                {
                    File.Delete(sourceFilePath);
                    Console.WriteLine($"○ 已删除处理后的源文件：【{sourceFilePath}】");
                }
            }
            catch (Exception)
            {
                Console.WriteLine($"源文件删除失败！请手动操作: \n" +
                                  $"1.删除源文件【{sourceFilePath}】（如存在）\n" +
                                  $"2.将备份文件【{backupFilePath}】重命名为源文件名");
                return;
            }
            
            // ======================== 将备份文件重命名为源文件名 ========================
            try
            {
                if (File.Exists(backupFilePath))
                {
                    File.Move(backupFilePath, sourceFilePath);
                    Console.WriteLine($"○ 已恢复备份文件为：【{sourceFilePath}】");
                }
                else
                {
                    Console.WriteLine($"警告：备份文件不存在【{backupFilePath}】");
                    return;
                }
            }
            catch (Exception)
            {
                Console.WriteLine($"备份文件重命名失败！请手动操作: \n" +
                                  $"将备份文件【{backupFilePath}】重命名为【{sourceFilePath}】");
                return;
            }
            
            Console.WriteLine("单文件回滚成功完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"单文件回滚出现未知异常：{ex.Message}\n" +
                              $"请检查源文件与备份文件的状态，手动恢复正确的文件");
        }
    }
}