// ==============================================
// UnitConverter.cs - 单位转换工具类
// 功能：实现厘米、磅、英寸之间的单位换算
// ==============================================

namespace WordProcessorLib.Utilities;

/// <summary>
/// 单位转换工具类（静态类，线程安全）
/// 包含厘米、磅、英寸之间的精确转换方法
/// 转换公式参考国际标准单位定义
/// </summary>
public static class UnitConverter
{
    // ====================== 单位转换常量 ======================
    
    // 1 英寸 = 72 磅 (国际标准定义)
    private const double PointsPerInch = 72.0;
    
    // 1 英寸 = 2.54 厘米 (国际标准定义)
    private const double CentimetersPerInch = 2.54;

    // ====================== 转换系数 ======================
    
    // 厘米转磅系数：1 厘米 = (72 / 2.54) 磅 ≈ 28.3464566929 磅
    private const double CmToPointsFactor = PointsPerInch / CentimetersPerInch;
    
    // 磅转厘米系数：1 磅 = (2.54 / 72) 厘米 ≈ 0.0352777778 厘米
    private const double PointsToCmFactor = 1 / CmToPointsFactor;
    
    // 英寸转厘米系数：1 英寸 = 2.54 厘米
    private const double InchToCmFactor = CentimetersPerInch;
    
    // 厘米转英寸系数：1 厘米 = 1 / 2.54 英寸 ≈ 0.393700787 英寸
    private const double CmToInchFactor = 1 / CentimetersPerInch;

    // ====================== 公共转换方法 ======================

    /// 厘米转磅（保留4位小数精度）
    public static double CmToPoints(double centimeters)
    {
        return centimeters * CmToPointsFactor;
    }

    /// 英寸转磅（保留4位小数精度）
    public static double InchToPoints(double inches)
    {
        return inches * PointsPerInch;
    }

    /// 磅转厘米（保留4位小数精度）
    public static double PointsToCm(double points)
    {
        return points * PointsToCmFactor;
    }

    /// 磅转英寸（保留4位小数精度）
    public static double PointsToInch(double points)
    {
        return points / PointsPerInch;
    }

    /// 厘米转英寸（保留4位小数精度）
    public static double CmToInch(double centimeters)
    {
        return centimeters * CmToInchFactor;
    }

    /// 英寸转厘米（保留4位小数精度）
    public static double InchToCm(double inches)
    {
        return inches * InchToCmFactor;
    }
}