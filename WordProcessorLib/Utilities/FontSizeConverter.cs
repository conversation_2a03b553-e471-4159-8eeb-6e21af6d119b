namespace WordProcessorLib.Utilities;

/// 中文印刷字号与磅值映射表
public static class FontSizeConverter
{
    private static readonly IReadOnlyDictionary<string, double> _sizeMapping;

    static FontSizeConverter()
    {
        // 初始化字号映射表
        _sizeMapping = new Dictionary<string, double>
        {
            ["初号"] = 42.0, ["小初"] = 36.0, ["一号"] = 26.0, ["小一"] = 24.0,
            ["二号"] = 22.0, ["小二"] = 18.0, ["三号"] = 16.0, ["小三"] = 15.0,
            ["四号"] = 14.0, ["小四"] = 12.0, ["五号"] = 10.5, ["小五"] = 9.0,
            ["六号"] = 7.5, ["小六"] = 6.5, ["七号"] = 5.5, ["八号"] = 5.0
        }.AsReadOnly(); // 转换为只读字典保证线程安全
    }
    
    /// 获取中文字号对应的磅值
    public static double GetPointSize(string chineseSize)
    {
        if (!_sizeMapping.TryGetValue(chineseSize, out var size))
            throw new ArgumentException($"无效的中文字号: {chineseSize}");
        return size;
    }
}