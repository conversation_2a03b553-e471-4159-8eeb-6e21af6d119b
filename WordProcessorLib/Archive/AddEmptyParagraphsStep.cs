using Aspose.Words;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Services;

// ================================================================================
// 文件：AddEmptyParagraphsStep.cs
// 归档说明：
// 此类曾用于增加指定字段前面的空行数，现已将该功能合并到 SetEmptyParagraphsStep.cs 里，
// SetEmptyParagraphsStep 可以做到调整指定字段与上个文字段之间的空行数。
// 此类要使用时须将以下代码添加到控制台程序：
// const string markerToAdd = "【答案】";   // 要查找的段首标识
// const int spacesToAdd = 3;             // 需要添加的空行数
// ================================================================================

namespace WordProcessorLib.Archive;

public class AddEmptyParagraphsStep : IPipelineStep
{
    private readonly string _marker;
    private readonly int _spaces;

    // 简化构造函数（移除参数校验）
    public AddEmptyParagraphsStep(string marker, int spaces)
    {
        _marker = marker.Trim();
        _spaces = spaces;
    }

    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 直接处理第一个正文区域
            ProcessBody(doc.FirstSection.Body);
            return true;
        }
        catch
        {
            RollbackService.Execute(filePath, $"{filePath}.bak");
            return false;
        }
    }

    private void ProcessBody(Body body)
    {
        // 获取所有段落（不排除页眉页脚）
        var paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .ToList();

        // 逆向遍历核心逻辑
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            var para = paragraphs[i];
            if (para.Range?.Text.StartsWith(_marker) == true)
            {
                InsertSpaces(para);
            }
        }
    }

    private void InsertSpaces(Paragraph target)
    {
        var parent = target.ParentNode;
        for (int i = 0; i < _spaces; i++)
        {
            var space = new Paragraph(target.Document);
            space.AppendChild(new Run(target.Document, ""));
            parent.InsertBefore(space, target);
        }
    }
}









/*
/// 可正确执行的进阶版（增加各种校验）
/// 段落间距调整步骤（最终稳定版）
/// 功能：在指定段首标记的段落前插入固定数量空行
public class AddSpacesBeforeMarkerStep : IPipelineStep
{
    private readonly string _targetMarker;
    private readonly int _spacesToAdd;

    /// 构造函数（强制参数校验）
    /// <param name="targetMarker">需匹配的段首标识（非空）</param>
    /// <param name="spacesToAdd">需添加的空行数（≥0）</param>
    /// <exception cref="ArgumentException">参数无效时抛出</exception>
    public AddSpacesBeforeMarkerStep(string targetMarker, int spacesToAdd)
    {
        // 参数有效性验证
        if (string.IsNullOrWhiteSpace(targetMarker))
            throw new ArgumentException("段首标识不能为空或空白字符", nameof(targetMarker));
        
        if (spacesToAdd < 0)
            throw new ArgumentException("空行数不能为负数", nameof(spacesToAdd));

        _targetMarker = targetMarker.Trim();
        _spacesToAdd = spacesToAdd;
    }

    /// 实现接口方法（核心处理流程）
    /// <param name="doc">Word文档对象</param>
    /// <param name="filePath">当前文件路径</param>
    /// <returns>处理结果（true=成功）</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 空操作快速返回
            if (_spacesToAdd == 0)
            {
                Console.WriteLine($"跳过处理：{Path.GetFileName(filePath)}（空行数为0）");
                return true;
            }

            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body, filePath);
            }
            return true;
        }
        catch (Exception ex)
        {
            HandleError(filePath, ex);
            return false;
        }
    }

    /// 处理文档正文内容
    private void ProcessBody(Body body, string filePath)
    {
        // 获取所有有效段落（排除页眉页脚）
        var paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => !IsInHeaderFooter(p))
            .ToList();

        Console.WriteLine($"[{Path.GetFileName(filePath)}] 发现 {paragraphs.Count} 个候选段落");

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsMatch(current))
            {
                Console.WriteLine($"定位到目标段落：{GetTrimmedText(current)}");
                InsertSpaces(current);
            }
        }
    }

    /// 判断段落是否在页眉页脚中
    private static bool IsInHeaderFooter(Paragraph para)
    {
        return para.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// 执行空行插入操作
    private void InsertSpaces(Paragraph target)
    {
        CompositeNode parent = target.ParentNode;
        NodeCollection siblings = parent.GetChildNodes(NodeType.Any, false);
        int targetIndex = siblings.IndexOf(target);

        if (targetIndex == -1)
        {
            Console.WriteLine("⚠️ 目标段落未找到于直接子节点中");
            return;
        }

        // 创建文档实例（解决类型问题）
        Document doc = (Document)target.Document;

        for (int i = 0; i < _spacesToAdd; i++)
        {
            Paragraph space = CreateSpaceParagraph(doc);
            parent.InsertBefore(space, target);
            Console.WriteLine($"✅ 插入成功（序号：{i+1}）");
        }
    }

    /// 创建合法空白段落
    private static Paragraph CreateSpaceParagraph(Document doc)
    {
        Paragraph para = new Paragraph(doc);
        // 必须包含Run节点（否则段落无效）
        para.AppendChild(new Run(doc, string.Empty));
        // 清除默认格式
        para.ParagraphFormat.SpaceAfter = 0;
        para.ParagraphFormat.SpaceBefore = 0;
        return para;
    }

    /// 段落匹配逻辑（精确段首匹配）
    private bool IsMatch(Paragraph para)
    {
        string text = para.Range?.Text ?? string.Empty;
        // 处理首部空白字符
        return text.TrimStart().StartsWith(_targetMarker, StringComparison.Ordinal);
    }

    /// 获取段落简略文本（用于日志）
    private static string GetTrimmedText(Paragraph para)
    {
        string raw = para.Range?.Text ?? string.Empty;
        return raw.Length > 20 ? raw[..17] + "..." : raw.Trim();
    }

    /// 统一错误处理
    private void HandleError(string filePath, Exception ex)
    {
        string fileName = Path.GetFileName(filePath);
        Console.ForegroundColor = ConsoleColor.Red;
        Console.WriteLine($"‼️ 处理失败：{fileName}");
        Console.WriteLine($"   错误类型：{ex.GetType().Name}");
        Console.WriteLine($"   错误信息：{ex.Message}");
        Console.ResetColor();

        Console.WriteLine($"执行回滚操作...");
        RollbackService.Execute(filePath, $"{filePath}.bak");
    }
}
*/