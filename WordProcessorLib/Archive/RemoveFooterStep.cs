using Aspose.Words;
using WordProcessorLib.Interfaces;

// ============================================================
// 文件：RemoveFooterStep.cs
// 归档说明：
// 此类曾用于清除页脚，现已将该功能合并到 SetPageNumberStep.cs 里，
// SetPageNumberStep 可以同时做到页脚清除与插入新页码。
// ============================================================

namespace WordProcessorLib.Archive;

/// <summary>
/// 删除页脚步骤
/// </summary>
public class RemoveFooterStep : IPipelineStep
{
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            foreach (Section section in doc.Sections)
            {
                // 删除所有页脚内容
                HeaderFooter footer = section.HeadersFooters[HeaderFooterType.FooterPrimary];
                footer?.RemoveAllChildren();
            }
            return true;
        }
        catch (Exception ex)
        {
            throw new Exception($"页脚删除失败 [{filePath}]: {ex.Message}");
        }
    }
}