using Aspose.Words;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Services;

// ================================================================================
// 文件：RemoveEmptyParagraphsStep.cs
// 归档说明：
// 此类曾用于较少指定字段前面的空行数，现已将该功能合并到 SetEmptyParagraphsStep.cs 里，
// SetEmptyParagraphsStep 可以做到调整指定字段与上个文字段之间的空行数。
// 此类要使用时须将以下代码添加到控制台程序：
// const string markerToRemove = "【答案】";     // 要查找的段首标识
// const int spacesToRemove = 3;               // 需要减少的空行数
// ================================================================================

namespace WordProcessorLib.Archive;

/// <summary>
/// 段落前空行减少步骤
/// 功能：在指定段首字段的段落前减少指定数量的空行
/// 版本：Aspose.Words 25.4.0
/// </summary>
public class RemoveEmptyParagraphsStep : IPipelineStep
{
    private readonly string _targetMarker;
    private readonly int _spacesToRemove;

    /// <summary>
    /// 构造函数初始化配置参数
    /// </summary>
    /// <param name="targetMarker">需要匹配的段首标识</param>
    /// <param name="spacesToRemove">需要减少的空行数（必须≥0）</param>
    public RemoveEmptyParagraphsStep(string targetMarker, int spacesToRemove)
    {
        // 参数有效性验证
        _targetMarker = targetMarker?.Trim() ?? throw new ArgumentNullException(nameof(targetMarker));
        _spacesToRemove = spacesToRemove >= 0 ? spacesToRemove 
            : throw new ArgumentException("减少空行数不能为负数");
    }

    /// <summary>
    /// 实现接口方法（核心入口）
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body);
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理失败：{Path.GetFileName(filePath)}\n原因：{ex.Message}");
            RollbackService.Execute(filePath, $"{filePath}.bak");
            return false;
        }
    }

    /// <summary>
    /// 处理单个正文区域
    /// </summary>
    private void ProcessBody(Body body)
    {
        // 获取所有普通段落（排除页眉页脚）
        List<Paragraph> paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null)
            .ToList();

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsTargetParagraph(current))
            {
                RemoveSpacesBefore(current);
            }
        }
    }

    /// <summary>
    /// 判断是否为目标段落（段首精确匹配）
    /// </summary>
    private bool IsTargetParagraph(Paragraph para)
    {
        return para.Range?.Text.TrimStart().StartsWith(_targetMarker) == true;
    }

    /// <summary>
    /// 移除目标段落前的空行
    /// </summary>
    private void RemoveSpacesBefore(Paragraph target)
    {
        CompositeNode parent = target.ParentNode;
        NodeCollection siblings = parent.GetChildNodes(NodeType.Any, false);
        int targetIndex = siblings.IndexOf(target);

        if (targetIndex < 1) return; // 没有前导段落

        // 查找可删除的空段落
        List<Node> spacesToRemove = new List<Node>();
        for (int i = targetIndex - 1; i >= 0; i--)
        {
            Node node = siblings[i];
            if (node is Paragraph p && IsEmptyParagraph(p))
            {
                spacesToRemove.Add(node);
                if (spacesToRemove.Count >= _spacesToRemove) break;
            }
            else
            {
                break; // 遇到非空段落停止
            }
        }

        // 执行删除操作
        foreach (Node space in spacesToRemove)
        {
            space.Remove();
            Console.WriteLine($"移除空段落：{space.GetText()}");
        }
    }

    /// <summary>
    /// 判断是否为空段落（包含空白字符）
    /// </summary>
    private static bool IsEmptyParagraph(Paragraph para)
    {
        return string.IsNullOrWhiteSpace(para.GetText());
    }
}