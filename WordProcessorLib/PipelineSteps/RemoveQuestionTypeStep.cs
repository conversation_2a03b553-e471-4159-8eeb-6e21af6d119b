using System;
using System.Drawing;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 实现删除包含特定字段段落的处理步骤
/// </summary>
public class RemoveQuestionTypeStep : IPipelineStep
{
    private readonly HashSet<string> _targetFields;
    
    /// <summary>
    /// 内部类：题型标记信息
    /// </summary>
    private class QuestionTypeInfo
    {
        public int Index { get; set; }        // 段落索引
        public Paragraph Paragraph { get; set; }  // 段落引用
        public string Text { get; set; }      // 段落文本
        public bool IsFirst { get; set; }     // 是否为第一个题型标记
    }

    public RemoveQuestionTypeStep(List<string> targetFields)
    {
        _targetFields = new HashSet<string>(targetFields);
    }

    public bool Execute(Document doc, string filePath)
    {
        try
        {
            List<Paragraph> validParagraphs = GetValidParagraphs(doc);
            List<int> indicesToRemove = new List<int>();
            List<QuestionTypeInfo> questionTypeInfos = new List<QuestionTypeInfo>();

            // 第一步：识别所有题型标记
            for (int i = 0; i < validParagraphs.Count; i++)
            {
                Paragraph para = validParagraphs[i];
                string text = para.GetText().Trim();

                if (IsQuestionTypeHeader(text) && !HasSpecialContent(para))
                {
                    questionTypeInfos.Add(new QuestionTypeInfo
                    {
                        Index = i,
                        Paragraph = para,
                        Text = text,
                        IsFirst = questionTypeInfos.Count == 0  // 第一个题型标记
                    });

                    // 标记当前段落
                    indicesToRemove.Add(i);

                    // 向前扫描连续空段落
                    ScanAdjacentEmptyParagraphs(validParagraphs, i, -1, indicesToRemove); // 向上扫描
                    ScanAdjacentEmptyParagraphs(validParagraphs, i, +1, indicesToRemove); // 向下扫描
                }
            }

            // 第二步：执行删除并为非第一个题型标记插入空行（倒序）
            var sortedIndices = indicesToRemove.Distinct().OrderByDescending(x => x).ToList();
            
            foreach (int index in sortedIndices)
            {
                Paragraph paraToRemove = validParagraphs[index];
                
                // 检查是否为非第一个题型标记
                var qtInfo = questionTypeInfos.FirstOrDefault(q => q.Index == index);
                bool shouldInsertEmptyLine = qtInfo != null && !qtInfo.IsFirst;
                
                // 在删除前记录父节点和下一个兄弟节点（用于插入空行）
                CompositeNode parent = null;
                Node nextSibling = null;
                
                if (shouldInsertEmptyLine)
                {
                    parent = paraToRemove.ParentNode;
                    nextSibling = paraToRemove.NextSibling;
                }
                
                // 执行删除
                paraToRemove.Remove();
                
                // 为非第一个题型标记插入空行
                if (shouldInsertEmptyLine && parent != null)
                {
                    InsertEmptyLineAt(parent, nextSibling);
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理文件 {filePath} 时出错: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 检查段落文本是否为题型标题
    /// </summary>
    /// <param name="text">段落文本</param>
    /// <returns>是否为题型标题</returns>
    private bool IsQuestionTypeHeader(string text)
    {
        // 使用前缀匹配，检查段落开头是否包含任何目标字段
        foreach (string headerText in _targetFields)
        {
            if (text.StartsWith(headerText))
            {
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// 在指定位置插入空行（参照InsertEndTagStep的实现）
    /// </summary>
    /// <param name="parent">父节点</param>
    /// <param name="nextSibling">下一个兄弟节点，如果为null则插入到末尾</param>
    private void InsertEmptyLineAt(CompositeNode parent, Node nextSibling)
    {
        try
        {
            Document doc = (Document)parent.Document;
            
            // 创建空段落
            Paragraph emptyPara = new Paragraph(doc);
            Run emptyRun = new Run(doc, "");
            emptyPara.AppendChild(emptyRun);
            
            // 清除底纹填充色（设置为透明）
            emptyPara.ParagraphFormat.Shading.BackgroundPatternColor = Color.Empty;
            
            // 清除底纹图案类型（设为无填充）
            emptyPara.ParagraphFormat.Shading.Texture = TextureIndex.TextureNone;
            
            // 清除所有边框
            emptyPara.ParagraphFormat.Borders.ClearFormatting();
            
            // 插入空段落
            if (nextSibling != null)
            {
                parent.InsertBefore(emptyPara, nextSibling);
            }
            else
            {
                parent.AppendChild(emptyPara);
            }
        }
        catch (Exception)
        {
            // 静默处理插入空行失败的情况
        }
    }

    /// <summary>
    /// 扫描相邻连续空段落的核心方法
    /// </summary>
    /// <param name="paragraphs">有效段落集合</param>
    /// <param name="startIndex">起始索引</param>
    /// <param name="step">扫描方向（-1=向上，+1=向下）</param>
    /// <param name="indices">待删除索引集合</param>
    private void ScanAdjacentEmptyParagraphs(
        List<Paragraph> paragraphs, 
        int startIndex, 
        int step, 
        List<int> indices)
    {
        int current = startIndex + step; // 从目标段落相邻位置开始

        // 循环条件：索引在有效范围内且段落为空
        while (current >= 0 && current < paragraphs.Count)
        {
            if (IsEmptyParagraph(paragraphs[current]))
            {
                indices.Add(current);
                current += step; // 继续向指定方向移动
            }
            else
            {
                break; // 遇到非空段落立即停止
            }
        }
    }

    // 以下方法与原始代码保持不变（为保持完整保留）
    private List<Paragraph> GetValidParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        foreach (Paragraph para in doc.GetChildNodes(NodeType.Paragraph, true))
        {
            if (!IsInForbiddenZone(para))
            {
                paragraphs.Add(para);
            }
        }
        return paragraphs;
    }

    private bool IsInForbiddenZone(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Table || parent is HeaderFooter)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }

    private bool HasSpecialContent(Paragraph para)
    {
        foreach (Node node in para.GetChildNodes(NodeType.Any, false))
        {
            if (node is Shape shape && shape.HasImage)
            {
                return true;
            }
        }
        return false;
    }

    private bool IsEmptyParagraph(Paragraph para)
    {
        return 
            string.IsNullOrWhiteSpace(para.GetText()) && 
            !HasSpecialContent(para);
    }
}