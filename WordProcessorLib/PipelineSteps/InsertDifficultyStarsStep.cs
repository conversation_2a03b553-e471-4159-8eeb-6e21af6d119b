using Aspose.Words;
using Aspose.Words.Tables;
using Aspose.Words.Drawing;
using WordProcessorLib.Interfaces;
using System.Text.RegularExpressions;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 插入难度星级标签步骤
/// 功能：根据【难度】标记在题号后插入相应的难度星级标签［★］～［★★★★★］
/// 处理流程：
/// 1. 查找【难度】+数字的段落
/// 2. 向前查找【答案】段落
/// 3. 向前查找题号段落
/// 4. 删除已有的难度星级标签
/// 5. 在题号后的第一个位置插入新的难度星级标签
/// 支持跨Run识别和处理各种Unicode空格字符
/// 支持自定义中文字体设置
/// </summary>
public class InsertDifficultyStarsStep : IPipelineStep
{
    // 难度星级标签定义
    private static readonly string[] DifficultyStars = {
        "［★］",        // 0.85 < difficulty <= 1
        "［★★］",       // 0.65 < difficulty <= 0.85
        "［★★★］",      // 0.4 < difficulty <= 0.65
        "［★★★★］",     // 0.15 < difficulty <= 0.4
        "［★★★★★］"     // 0 < difficulty <= 0.15
    };
    
    private static readonly HashSet<string> AllDifficultyStars = new HashSet<string>(DifficultyStars);
    
    private readonly string? _cjkFont; // 中文字体配置，可为null
    
    /// <summary>
    /// 构造函数，初始化字体配置
    /// </summary>
    /// <param name="cjkFont">中文字体名称，用于设置难度星级标签的字体，如果为null则不设置字体</param>
    public InsertDifficultyStarsStep(string? cjkFont)
    {
        _cjkFont = cjkFont;
    }
    
    /// <summary>
    /// 实现接口核心方法
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            int skippedCount = 0;
            
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                skippedCount += ProcessSection(section);
            }
            
            // 输出跳过的难度标记信息
            if (skippedCount > 0)
            {
                Console.WriteLine($"文档 {Path.GetFileName(filePath)} 跳过了 {skippedCount} 个难度标记");
            }
            
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"InsertDifficultyStarsStep 执行失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 处理单个章节
    /// </summary>
    private int ProcessSection(Section section)
    {
        int skippedCount = 0;
        
        // 获取所有普通段落（排除页眉页脚和表格内段落）
        List<Paragraph> paragraphs = section.Body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null && !IsInTable(p))
            .ToList();

        // 逆向遍历防止处理过程中的索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            var difficultyInfo = ParseDifficultyParagraph(current);
            
            if (difficultyInfo != null)
            {
                if (!ProcessDifficultyParagraph(paragraphs, i, difficultyInfo))
                {
                    skippedCount++;
                }
            }
        }
        
        return skippedCount;
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    private bool IsInTable(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }

    /// <summary>
    /// 解析难度段落，如果是【难度】+数字格式则返回难度值
    /// </summary>
    private DifficultyInfo? ParseDifficultyParagraph(Paragraph para)
    {
        // 构建段落文本信息（仅来自Run节点）
        var textInfo = BuildParagraphTextInfo(para);
        string fullText = textInfo.FullText;
        
        if (string.IsNullOrEmpty(fullText))
            return null;
        
        // 匹配【难度】+数字格式，支持各种Unicode空格
        // 模式：（可选空格）【难度】（可选空格）数字（可选空格）
        Regex difficultyPattern = new Regex(@"^\s*【难度】\s*([0-9]+(?:\.[0-9]+)?)\s*$", RegexOptions.Compiled);
        Match match = difficultyPattern.Match(fullText);
        
        if (match.Success)
        {
            if (double.TryParse(match.Groups[1].Value, out double difficulty))
            {
                return new DifficultyInfo
                {
                    Difficulty = difficulty,
                    Paragraph = para,
                    TextInfo = textInfo
                };
            }
        }
        
        return null;
    }

    /// <summary>
    /// 处理难度段落，返回是否处理成功
    /// </summary>
    private bool ProcessDifficultyParagraph(List<Paragraph> paragraphs, int difficultyIndex, DifficultyInfo difficultyInfo)
    {
        // 向前查找【答案】段落
        Paragraph? answerParagraph = FindAnswerParagraph(paragraphs, difficultyIndex);
        if (answerParagraph == null)
        {
            return false; // 未找到答案段落，跳过
        }
        
        // 向前查找题号段落
        int answerIndex = paragraphs.IndexOf(answerParagraph);
        Paragraph? questionParagraph = FindQuestionParagraph(paragraphs, answerIndex);
        if (questionParagraph == null)
        {
            return false; // 未找到题号段落，跳过
        }
        
        // 获取对应的难度星级标签
        string starTag = GetDifficultyStarTag(difficultyInfo.Difficulty);
        
        // 在题号段落插入难度星级标签
        InsertDifficultyStarInQuestion(questionParagraph, starTag);
        
        return true;
    }

    /// <summary>
    /// 向前查找【答案】段落
    /// </summary>
    private Paragraph? FindAnswerParagraph(List<Paragraph> paragraphs, int startIndex)
    {
        for (int i = startIndex - 1; i >= 0; i--)
        {
            Paragraph para = paragraphs[i];
            if (IsAnswerParagraph(para))
            {
                return para;
            }
        }
        return null;
    }

    /// <summary>
    /// 判断是否为【答案】段落
    /// </summary>
    private bool IsAnswerParagraph(Paragraph para)
    {
        var textInfo = BuildParagraphTextInfo(para);
        string fullText = textInfo.FullText;
        
        if (string.IsNullOrEmpty(fullText))
            return false;
        
        // 匹配以【答案】开头的段落，前面可能有空格
        Regex answerPattern = new Regex(@"^\s*【答案】", RegexOptions.Compiled);
        return answerPattern.IsMatch(fullText);
    }

    /// <summary>
    /// 向前查找题号段落（参考SetQuestionSpacesStep的实现）
    /// </summary>
    private Paragraph? FindQuestionParagraph(List<Paragraph> paragraphs, int startIndex)
    {
        for (int i = startIndex - 1; i >= 0; i--)
        {
            Paragraph para = paragraphs[i];
            if (IsQuestionParagraph(para))
            {
                return para;
            }
        }
        return null;
    }

    /// <summary>
    /// 判断是否为题号段落（严格匹配数字+中文点"．"格式）
    /// 参考SetQuestionSpacesStep的实现
    /// </summary>
    private bool IsQuestionParagraph(Paragraph para)
    {
        string? text = para.Range?.Text?.TrimStart();
        if (string.IsNullOrEmpty(text))
            return false;
            
        // 严格匹配题号格式：段落开头的数字+中文点"．"
        Regex questionPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
        return questionPattern.IsMatch(text);
    }

    /// <summary>
    /// 根据难度值获取对应的星级标签
    /// </summary>
    private string GetDifficultyStarTag(double difficulty)
    {
        if (difficulty > 0.85 && difficulty <= 1)
            return DifficultyStars[0];    // ［★］
        else if (difficulty > 0.65 && difficulty <= 0.85)
            return DifficultyStars[1];    // ［★★］
        else if (difficulty > 0.4 && difficulty <= 0.65)
            return DifficultyStars[2];    // ［★★★］
        else if (difficulty > 0.15 && difficulty <= 0.4)
            return DifficultyStars[3];    // ［★★★★］
        else if (difficulty > 0 && difficulty <= 0.15)
            return DifficultyStars[4];    // ［★★★★★］
        else
            return DifficultyStars[2];    // 默认值［★★★］
    }

    /// <summary>
    /// 在题号段落插入难度星级标签
    /// </summary>
    private void InsertDifficultyStarInQuestion(Paragraph questionPara, string starTag)
    {
        // 获取段落中所有的Run节点
        var runs = questionPara.GetChildNodes(NodeType.Run, true)
            .OfType<Run>()
            .ToList();
            
        if (runs.Count == 0) return;

        // 构建Run映射关系
        var runMappings = BuildRunMappings(runs);
        string fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));
        
        // 1. 先删除已有的难度星级标签
        RemoveExistingDifficultyStars(runMappings, fullText);
        
        // 重新构建映射（因为删除操作可能改变了Run结构）
        runs = questionPara.GetChildNodes(NodeType.Run, true).OfType<Run>().ToList();
        runMappings = BuildRunMappings(runs);
        fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));
        
        // 2. 找到插入位置（题号后的第一个位置）
        int insertPosition = FindInsertPosition(fullText);
        
        // 3. 在指定位置插入难度星级标签
        InsertStarTagAtPosition(runMappings, insertPosition, starTag);
    }

    /// <summary>
    /// 删除已有的难度星级标签
    /// </summary>
    private void RemoveExistingDifficultyStars(List<RunMapping> runMappings, string fullText)
    {
        // 查找所有难度星级标签的位置
        var starPositions = new List<(int start, int end, string tag)>();
        
        foreach (string starTag in AllDifficultyStars)
        {
            int index = 0;
            while (index < fullText.Length)
            {
                int foundIndex = fullText.IndexOf(starTag, index, StringComparison.Ordinal);
                if (foundIndex == -1) break;
                
                starPositions.Add((foundIndex, foundIndex + starTag.Length, starTag));
                index = foundIndex + starTag.Length;
            }
        }
        
        // 按位置从后往前删除
        starPositions.Sort((a, b) => b.start.CompareTo(a.start));
        
        foreach (var (start, end, tag) in starPositions)
        {
            RemoveTextRange(runMappings, start, end);
            
            // 重新构建映射（因为删除操作可能改变了Run结构）
            var paragraph = runMappings.FirstOrDefault()?.Run?.ParentNode as Paragraph;
            if (paragraph != null)
            {
                var runs = paragraph.GetChildNodes(NodeType.Run, true).OfType<Run>().ToList();
                runMappings.Clear();
                runMappings.AddRange(BuildRunMappings(runs));
            }
        }
    }

    /// <summary>
    /// 删除指定文本范围
    /// </summary>
    private void RemoveTextRange(List<RunMapping> runMappings, int startIndex, int endIndex)
    {
        // 找到涉及的Run范围
        var affectedRuns = runMappings
            .Where(rm => rm.StartPosition < endIndex && rm.EndPosition > startIndex)
            .OrderBy(rm => rm.StartPosition)
            .ToList();

        foreach (var runMapping in affectedRuns)
        {
            // 计算在当前Run中的相对位置
            int runStart = Math.Max(0, startIndex - runMapping.StartPosition);
            int runEnd = Math.Min(runMapping.Run.Text?.Length ?? 0, endIndex - runMapping.StartPosition);
            
            if (runStart >= runEnd) continue;
            
            string runText = runMapping.Run.Text ?? "";
            
            if (runStart == 0 && runEnd == runText.Length)
            {
                // 整个Run都要删除
                runMapping.Run.Remove();
            }
            else
            {
                // 部分删除
                string beforePart = runStart > 0 ? runText.Substring(0, runStart) : "";
                string afterPart = runEnd < runText.Length ? runText.Substring(runEnd) : "";
                runMapping.Run.Text = beforePart + afterPart;
            }
        }
    }

    /// <summary>
    /// 找到插入位置（题号后的第一个位置）
    /// </summary>
    private int FindInsertPosition(string fullText)
    {
        // 查找题号结束位置，直接在题号后插入
        Regex questionEndPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
        Match questionMatch = questionEndPattern.Match(fullText);
        if (questionMatch.Success)
        {
            return questionMatch.Index + questionMatch.Length;
        }
        
        return 0; // 默认插入到开头
    }

    /// <summary>
    /// 在指定位置插入星级标签
    /// </summary>
    private void InsertStarTagAtPosition(List<RunMapping> runMappings, int position, string starTag)
    {
        // 找到插入位置对应的Run
        var targetRun = runMappings.FirstOrDefault(rm => 
            rm.StartPosition <= position && rm.EndPosition >= position);
        
        if (targetRun == null)
        {
            // 如果没有找到合适的Run，在最后一个Run后面插入
            var lastRun = runMappings.LastOrDefault();
            if (lastRun != null)
            {
                Run newRun = CloneRun(lastRun.Run, starTag);
                lastRun.Run.ParentNode.InsertAfter(newRun, lastRun.Run);
            }
            return;
        }
        
        int positionInRun = position - targetRun.StartPosition;
        string runText = targetRun.Run.Text ?? "";
        
        if (positionInRun == 0)
        {
            // 在Run开头插入
            Run newRun = CloneRun(targetRun.Run, starTag);
            targetRun.Run.ParentNode.InsertBefore(newRun, targetRun.Run);
        }
        else if (positionInRun >= runText.Length)
        {
            // 在Run末尾插入
            Run newRun = CloneRun(targetRun.Run, starTag);
            targetRun.Run.ParentNode.InsertAfter(newRun, targetRun.Run);
        }
        else
        {
            // 在Run中间插入，需要分割
            SplitRunAndInsert(targetRun.Run, positionInRun, starTag);
        }
    }

    /// <summary>
    /// 分割Run并插入文本（参考SetPunctuationStep的实现）
    /// </summary>
    private void SplitRunAndInsert(Run originalRun, int splitPosition, string insertText)
    {
        string originalText = originalRun.Text ?? "";
        CompositeNode parent = originalRun.ParentNode;
        
        // 分割文本为两部分：前置和后置
        string beforeText = splitPosition > 0 ? originalText.Substring(0, splitPosition) : "";
        string afterText = splitPosition < originalText.Length ? originalText.Substring(splitPosition) : "";
        
        // 处理前置文本（如果有）
        if (!string.IsNullOrEmpty(beforeText))
        {
            Run beforeRun = CloneRun(originalRun, beforeText);
            parent.InsertBefore(beforeRun, originalRun);
        }
        
        // 处理插入文本
        Run insertRun = CloneRun(originalRun, insertText);
        parent.InsertBefore(insertRun, originalRun);
        
        // 处理后置文本（如果有）
        if (!string.IsNullOrEmpty(afterText))
        {
            Run afterRun = CloneRun(originalRun, afterText);
            parent.InsertBefore(afterRun, originalRun);
        }
        
        // 移除原始Run
        originalRun.Remove();
    }

    /// <summary>
    /// 构建段落文本信息（仅处理文本内容）
    /// 参考RemoveSpecialQuestionSourceStep的实现
    /// </summary>
    private ParagraphTextInfo BuildParagraphTextInfo(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        return new ParagraphTextInfo
        {
            FullText = fullText,
            RunMap = runMap
        };
    }

    /// <summary>
    /// 构建Run映射关系（参考ReplaceTextStep的实现）
    /// </summary>
    private List<RunMapping> BuildRunMappings(List<Run> runs)
    {
        var runMappings = new List<RunMapping>();
        int currentPosition = 0;

        foreach (var run in runs)
        {
            string runText = run.Text ?? "";
            runMappings.Add(new RunMapping
            {
                Run = run,
                StartPosition = currentPosition,
                EndPosition = currentPosition + runText.Length,
                Text = runText
            });
            currentPosition += runText.Length;
        }

        return runMappings;
    }

    /// <summary>
    /// 克隆Run并设置新文本，同时应用中文字体设置（参考SetPunctuationStep的实现）
    /// </summary>
    private Run CloneRun(Run sourceRun, string newText)
    {
        Run clonedRun = (Run)sourceRun.Clone(true);
        clonedRun.Text = newText;
        
        // 为难度星级标签应用中文字体设置（仅当字体不为null时）
        if (AllDifficultyStars.Contains(newText) && !string.IsNullOrEmpty(_cjkFont))
        {
            ApplyChineseStyle(clonedRun);
        }
        
        return clonedRun;
    }
    
    /// <summary>
    /// 应用中文字体样式到Run节点（参考SetPunctuationStep的实现）
    /// </summary>
    private void ApplyChineseStyle(Run run)
    {
        if (!string.IsNullOrEmpty(_cjkFont))
        {
            run.Font.Name = _cjkFont;
            run.Font.NameFarEast = _cjkFont;
            run.Font.Italic = false;
        }
    }

    /// <summary>
    /// 难度信息类
    /// </summary>
    private class DifficultyInfo
    {
        public double Difficulty { get; set; }
        public Paragraph Paragraph { get; set; } = null!;
        public ParagraphTextInfo TextInfo { get; set; } = null!;
    }

    /// <summary>
    /// 段落文本信息类
    /// </summary>
    private class ParagraphTextInfo
    {
        public string FullText { get; set; } = "";
        public List<(int start, int end, Run run)> RunMap { get; set; } = new List<(int, int, Run)>();
    }

    /// <summary>
    /// Run映射信息类
    /// </summary>
    private class RunMapping
    {
        public Run Run { get; set; } = null!;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public string Text { get; set; } = string.Empty;
    }
}
