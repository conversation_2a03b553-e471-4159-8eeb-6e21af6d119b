using System.Drawing;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Math;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 设置答案底纹处理步骤（实现IPipelineStep接口）
/// 功能：对【答案】段落到END标签段落之间的所有段落（包括图片、表格、OMML公式段落）设置底纹
/// </summary>
public class SetAnswerShadingStep : IPipelineStep
{
    private readonly string _shadingMode;

    /// <summary>
    /// 构造函数：初始化底纹模式
    /// </summary>
    /// <param name="shadingMode">底纹模式。"gray"为设置灰色底纹；"white"为设置白色底纹</param>
    public SetAnswerShadingStep(string shadingMode = "white")
    {
        _shadingMode = shadingMode ?? "white";
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>是否处理成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取所有段落
            List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
            
            // 找出所有答案段落及其对应的END标记
            var answerEndPairs = FindAnswerEndPairs(allParagraphs);
            
            if (answerEndPairs.Count == 0)
            {
                return true; // 没有答案区域，直接返回成功
            }

            // 处理每对答案和END标记之间的段落
            foreach (var (answerPara, endPara) in answerEndPairs)
            {
                ProcessAnswerAreaParagraphs(allParagraphs, answerPara, endPara);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 查找所有答案段落及其对应的END标记
    /// </summary>
    /// <param name="allParagraphs">所有段落列表</param>
    /// <returns>答案段落和END段落的配对列表</returns>
    private List<(Paragraph answerPara, Paragraph endPara)> FindAnswerEndPairs(List<Paragraph> allParagraphs)
    {
        var answerEndPairs = new List<(Paragraph answerPara, Paragraph endPara)>();
        var endMarkers = new List<string> { "【END1】", "【END2】", "【END3】", "【END4】" };

        // 先找出所有答案段落
        for (int i = 0; i < allParagraphs.Count; i++)
        {
            Paragraph para = allParagraphs[i];
            string text = para.GetText();

            if (text.Contains("【答案】"))
            {
                // 查找对应的END标记
                for (int j = i + 1; j < allParagraphs.Count; j++)
                {
                    Paragraph endPara = allParagraphs[j];
                    string endText = endPara.GetText().Trim();

                    // 检查是否是纯END标记段落
                    foreach (string endMarker in endMarkers)
                    {
                        if (endText == endMarker || endText == endMarker + "\r")
                        {
                            answerEndPairs.Add((para, endPara));
                            goto NextAnswer; // 找到对应的END标记后跳出循环
                        }
                    }
                }
                NextAnswer:;
            }
        }

        return answerEndPairs;
    }

    /// <summary>
    /// 处理答案区域内的所有段落底纹
    /// </summary>
    /// <param name="allParagraphs">所有段落列表</param>
    /// <param name="answerPara">答案段落</param>
    /// <param name="endPara">END标记段落</param>
    private void ProcessAnswerAreaParagraphs(List<Paragraph> allParagraphs, Paragraph answerPara, Paragraph endPara)
    {
        try
        {
            // 找到答案段落和END段落的索引
            int answerIndex = allParagraphs.IndexOf(answerPara);
            int endIndex = allParagraphs.IndexOf(endPara);

            if (answerIndex < 0 || endIndex < 0 || answerIndex >= endIndex)
            {
                return; // 索引无效
            }

            // 确定底纹颜色
            Color shadingColor = GetShadingColor();

            // 处理答案段落到END段落之间的所有段落（包括答案段落和END段落）
            for (int i = answerIndex; i <= endIndex; i++)
            {
                Paragraph para = allParagraphs[i];
                ApplyParagraphShading(para, shadingColor);
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 根据模式获取底纹颜色
    /// </summary>
    /// <returns>底纹颜色</returns>
    private Color GetShadingColor()
    {
        if (_shadingMode == "gray")
        {
            // 使用RGB(242, 242, 242)，这是Word中白色列下面第一个灰色的颜色值
            return Color.FromArgb(242, 242, 242);
        }
        else
        {
            // 白色底纹
            return Color.White;
        }
    }

    /// <summary>
    /// 对单个段落应用底纹
    /// </summary>
    /// <param name="paragraph">要设置底纹的段落</param>
    /// <param name="shadingColor">底纹颜色</param>
    private void ApplyParagraphShading(Paragraph paragraph, Color shadingColor)
    {
        try
        {
            // *** 关键：设置段落底纹 ***
            paragraph.ParagraphFormat.Shading.BackgroundPatternColor = shadingColor;
            paragraph.ParagraphFormat.Shading.ForegroundPatternColor = shadingColor;

            // *** 特殊处理：表格单元格内的段落 ***
            // 如果段落在表格单元格内，还需要设置单元格的底纹
            if (IsInsideTableCell(paragraph))
            {
                Cell? parentCell = GetParentCell(paragraph);
                if (parentCell != null)
                {
                    ApplyCellShading(parentCell, shadingColor);
                }
            }

            // *** 特殊处理：包含图片的段落 ***
            // 检查段落是否包含图片或形状，如果有，需要特别处理
            ProcessShapesInParagraph(paragraph, shadingColor);

            // *** 特殊处理：包含OMML公式的段落 ***
            // OMML公式的底纹需要特别设置
            ProcessOmmlInParagraph(paragraph, shadingColor);
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 判断段落是否在表格单元格内
    /// </summary>
    /// <param name="paragraph">要检查的段落</param>
    /// <returns>是否在表格单元格内</returns>
    private bool IsInsideTableCell(Paragraph paragraph)
    {
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }

    /// <summary>
    /// 获取段落所在的单元格
    /// </summary>
    /// <param name="paragraph">段落</param>
    /// <returns>单元格对象，如果不在单元格内则返回null</returns>
    private Cell? GetParentCell(Paragraph paragraph)
    {
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return (Cell)parent;
            }
            parent = parent.ParentNode;
        }
        return null;
    }

    /// <summary>
    /// 对表格单元格应用底纹
    /// </summary>
    /// <param name="cell">要设置底纹的单元格</param>
    /// <param name="shadingColor">底纹颜色</param>
    private void ApplyCellShading(Cell cell, Color shadingColor)
    {
        try
        {
            // 设置单元格的底纹
            cell.CellFormat.Shading.BackgroundPatternColor = shadingColor;
            cell.CellFormat.Shading.ForegroundPatternColor = shadingColor;
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 处理段落中的图片和形状
    /// </summary>
    /// <param name="paragraph">段落</param>
    /// <param name="shadingColor">底纹颜色</param>
    private void ProcessShapesInParagraph(Paragraph paragraph, Color shadingColor)
    {
        try
        {
            // 获取段落中的所有形状节点（包括图片）
            NodeCollection shapes = paragraph.GetChildNodes(NodeType.Shape, true);
            
            foreach (Shape shape in shapes.OfType<Shape>())
            {
                // 对形状设置底纹（如果支持的话）
                // 注意：图片本身通常不能设置底纹，但可以通过其他方式处理
                // 这里主要确保图片所在段落的底纹正确显示
                if (shape.Fill != null)
                {
                    // *** 修复关键bug：强制设置形状填充色，不依赖当前颜色判断 ***
                    try
                    {
                        // 强制设置形状填充色，确保白色和灰色底纹都能正确应用
                        shape.Fill.Color = shadingColor;
                    }
                    catch (Exception)
                    {
                        // 某些图片类型可能不支持填充色修改，静默忽略
                    }
                }
            }

            // 检查GroupShape（组合图形）
            NodeCollection groupShapes = paragraph.GetChildNodes(NodeType.GroupShape, true);
            foreach (GroupShape groupShape in groupShapes.OfType<GroupShape>())
            {
                // 处理组合图形内的子形状
                ProcessGroupShape(groupShape, shadingColor);
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 处理组合图形
    /// </summary>
    /// <param name="groupShape">组合图形</param>
    /// <param name="shadingColor">底纹颜色</param>
    private void ProcessGroupShape(GroupShape groupShape, Color shadingColor)
    {
        try
        {
            // 遍历组合图形中的所有子形状
            foreach (Node childNode in groupShape.GetChildNodes(NodeType.Shape, true))
            {
                if (childNode is Shape childShape && childShape.Fill != null)
                {
                    try
                    {
                        // *** 修复关键bug：强制设置组合图形中子形状的填充色 ***
                        childShape.Fill.Color = shadingColor;
                    }
                    catch (Exception)
                    {
                        // 静默忽略不支持的操作
                    }
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 处理段落中的OMML公式
    /// </summary>
    /// <param name="paragraph">段落</param>
    /// <param name="shadingColor">底纹颜色</param>
    private void ProcessOmmlInParagraph(Paragraph paragraph, Color shadingColor)
    {
        try
        {
            // 获取段落中的所有OMML公式节点
            NodeCollection ommlNodes = paragraph.GetChildNodes(NodeType.OfficeMath, true);
            
            foreach (OfficeMath omml in ommlNodes.OfType<OfficeMath>())
            {
                // OMML公式的底纹主要通过其所在段落的底纹来体现
                // 这里可以进行一些特殊处理，确保公式区域的底纹正确显示
                
                // 获取OMML公式中的所有Run节点
                NodeCollection runs = omml.GetChildNodes(NodeType.Run, true);
                foreach (Run run in runs.OfType<Run>())
                {
                    // *** 修复关键bug：强制设置OMML Run节点底纹，确保白色和灰色都能正确应用 ***
                    try
                    {
                        // 强制设置Run节点的底纹颜色，不依赖当前颜色判断
                        run.Font.Shading.BackgroundPatternColor = shadingColor;
                        run.Font.Shading.ForegroundPatternColor = shadingColor;
                    }
                    catch (Exception)
                    {
                        // 某些Run可能不支持底纹设置，静默忽略
                    }
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }
}
