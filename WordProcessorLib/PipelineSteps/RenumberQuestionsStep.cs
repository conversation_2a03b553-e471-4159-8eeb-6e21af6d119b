using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 题号重新排列步骤（实现IPipelineStep接口）
/// 功能：将题号按顺序重新排列，并校验题号与答案数量的一致性
/// </summary>
public sealed class RenumberQuestionsStep : IPipelineStep
{
    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中不在表格内的所有段落
            List<Paragraph> paragraphs = GetNonTableParagraphs(doc);
            
            // 识别所有题号段落
            List<QuestionInfo> questions = IdentifyQuestionParagraphs(paragraphs);
            
            // 统计答案数量
            int answerCount = CountAnswers(paragraphs);
            
            // 校验题号与答案数量的一致性
            if (questions.Count != answerCount)
            {
                // 在控制台输出详细的诊断信息
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine("✗ 发现数量不一致问题！");
                Console.ResetColor();
                
                Console.ForegroundColor = ConsoleColor.Yellow;
                Console.WriteLine($"  问题文档：{Path.GetFileName(filePath)}");
                Console.WriteLine($"  题号数量：{questions.Count}");
                Console.WriteLine($"  答案数量：{answerCount}");
                Console.ResetColor();
                
                throw new InvalidOperationException(
                    $"题号数量({questions.Count})与答案数量({answerCount})不一致！" +
                    $"文件：{Path.GetFileName(filePath)}");
            }
            
            // 重新编号题号
            RenumberQuestions(questions);
            
            return true;
        }
        catch (Exception)
        {
            // 重新抛出异常以触发回滚
            throw;
        }
    }
    
    /// <summary>
    /// 获取文档中不在表格内的所有段落
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonTableParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 跳过页眉页脚内容
            if (IsInHeaderFooter(para)) continue;
            
            // 检查是否在表格中
            bool isInTable = false;
            Node parent = para.ParentNode;
            while (parent != null)
            {
                if (parent is Cell)
                {
                    isInTable = true;
                    break;
                }
                parent = parent.ParentNode;
            }
            
            // 如果不在表格中，则添加到列表
            if (!isInTable)
            {
                paragraphs.Add(para);
            }
        }
        
        return paragraphs;
    }
    
    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    /// <param name="node">要检查的节点</param>
    /// <returns>是否在页眉页脚中</returns>
    private static bool IsInHeaderFooter(Node node)
    {
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }
    
    /// <summary>
    /// 构建段落Run映射信息
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <returns>段落文本和Run映射信息</returns>
    private ParagraphTextInfo BuildParagraphTextInfo(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        return new ParagraphTextInfo
        {
            FullText = fullText,
            RunMap = runMap
        };
    }
    
    /// <summary>
    /// 识别所有题号段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <returns>题号信息列表</returns>
    private List<QuestionInfo> IdentifyQuestionParagraphs(List<Paragraph> paragraphs)
    {
        List<QuestionInfo> questions = new List<QuestionInfo>();
        
        // 题号匹配正则表达式：严格匹配段落开头的数字+中文点"．"
        Regex questionPattern = new Regex(@"^(\d+)．", RegexOptions.Compiled);
        
        foreach (Paragraph paragraph in paragraphs)
        {
            // 构建段落文本信息
            ParagraphTextInfo textInfo = BuildParagraphTextInfo(paragraph);
            
            // 跳过空段落
            if (string.IsNullOrEmpty(textInfo.FullText))
            {
                continue;
            }
            
            // 检查是否匹配题号格式（严格匹配段落开头）
            Match match = questionPattern.Match(textInfo.FullText);
            if (match.Success)
            {
                // 提取原始题号
                int originalNumber = int.Parse(match.Groups[1].Value);
                string targetPattern = match.Value; // 完整的"数字．"
                
                // 查找题号在Run中的确切位置
                var questionRunInfo = FindPatternInRuns(textInfo, targetPattern, 0);
                
                if (questionRunInfo != null)
                {
                    questions.Add(new QuestionInfo
                    {
                        Paragraph = paragraph,
                        OriginalNumber = originalNumber,
                        QuestionRunInfo = questionRunInfo
                    });
                }
            }
        }
        
        return questions;
    }
    
    /// <summary>
    /// 在Run中查找指定模式的位置
    /// </summary>
    /// <param name="textInfo">段落文本信息</param>
    /// <param name="pattern">要查找的模式</param>
    /// <param name="startIndex">查找起始位置</param>
    /// <returns>模式Run信息</returns>
    private PatternRunInfo? FindPatternInRuns(ParagraphTextInfo textInfo, string pattern, int startIndex)
    {
        // 在完整文本中查找模式位置
        int patternIndex = textInfo.FullText.IndexOf(pattern, startIndex);
        if (patternIndex == -1) return null;
        
        int patternEnd = patternIndex + pattern.Length;
        
        // 查找涉及的Run
        List<Run> affectedRuns = new List<Run>();
        Run? primaryRun = null;
        int primaryRunStartIndex = -1;
        int primaryRunEndIndex = -1;
        
        foreach (var (start, end, run) in textInfo.RunMap)
        {
            if (patternIndex < end && patternEnd > start)
            {
                affectedRuns.Add(run);
                
                // 确定主要Run（包含模式开始位置的Run）
                if (primaryRun == null && patternIndex >= start && patternIndex < end)
                {
                    primaryRun = run;
                    primaryRunStartIndex = patternIndex - start;
                    primaryRunEndIndex = Math.Min(patternEnd - start, run.Text.Length);
                }
            }
        }
        
        if (affectedRuns.Count == 0 || primaryRun == null) return null;
        
        return new PatternRunInfo
        {
            TargetRun = primaryRun,
            AffectedRuns = affectedRuns,
            StartIndex = primaryRunStartIndex,
            EndIndex = primaryRunEndIndex,
            IsSingleRun = affectedRuns.Count == 1,
            FullTextStartIndex = patternIndex,
            FullTextEndIndex = patternEnd,
            TextInfo = textInfo
        };
    }
    
    /// <summary>
    /// 统计文档中的答案数量
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <returns>答案数量</returns>
    private int CountAnswers(List<Paragraph> paragraphs)
    {
        int answerCount = 0;
        
        // 答案匹配正则表达式：严格匹配段落开头的"【答案】"
        Regex answerPattern = new Regex(@"^【答案】", RegexOptions.Compiled);
        
        foreach (Paragraph paragraph in paragraphs)
        {
            // 构建段落文本信息
            ParagraphTextInfo textInfo = BuildParagraphTextInfo(paragraph);
            
            // 跳过空段落
            if (string.IsNullOrEmpty(textInfo.FullText))
            {
                continue;
            }
            
            // 检查是否匹配答案格式（严格匹配段落开头）
            if (answerPattern.IsMatch(textInfo.FullText))
            {
                answerCount++;
            }
        }
        
        return answerCount;
    }
    
    /// <summary>
    /// 重新编号所有题号
    /// </summary>
    /// <param name="questions">题号信息列表</param>
    private void RenumberQuestions(List<QuestionInfo> questions)
    {
        for (int i = 0; i < questions.Count; i++)
        {
            int newNumber = i + 1; // 从1开始编号
            QuestionInfo questionInfo = questions[i];
            
            // 只有当新号码与原号码不同时才进行更新
            if (newNumber != questionInfo.OriginalNumber)
            {
                UpdateQuestionNumber(questionInfo, newNumber);
            }
        }
    }
    
    /// <summary>
    /// 更新单个题号
    /// </summary>
    /// <param name="questionInfo">题号信息</param>
    /// <param name="newNumber">新的题号</param>
    private void UpdateQuestionNumber(QuestionInfo questionInfo, int newNumber)
    {
        string newNumberText = newNumber.ToString();
        string oldNumberText = questionInfo.OriginalNumber.ToString();
        
        if (questionInfo.QuestionRunInfo.IsSingleRun)
        {
            // 单个Run的情况 - 精确替换，保持格式边界
            UpdateQuestionNumberInSingleRun(questionInfo, newNumberText, oldNumberText);
        }
        else
        {
            // 跨Run的情况 - 精确处理每个Run
            UpdateQuestionNumberInMultipleRuns(questionInfo, newNumberText, oldNumberText);
        }
    }
    
    /// <summary>
    /// 在单个Run中更新题号，保持格式边界
    /// </summary>
    /// <param name="questionInfo">题号信息</param>
    /// <param name="newNumberText">新题号文本</param>
    /// <param name="oldNumberText">旧题号文本</param>
    private void UpdateQuestionNumberInSingleRun(QuestionInfo questionInfo, string newNumberText, string oldNumberText)
    {
        Run targetRun = questionInfo.QuestionRunInfo.TargetRun;
        int startIndex = questionInfo.QuestionRunInfo.StartIndex;
        int endIndex = questionInfo.QuestionRunInfo.EndIndex;
        
        string runText = targetRun.Text;
        
        // 精确查找数字部分的位置（不包括"．"）
        int numberStartIndex = startIndex;
        int numberEndIndex = startIndex + oldNumberText.Length;
        
        // 验证确实是要替换的数字
        if (numberEndIndex <= runText.Length && 
            runText.Substring(numberStartIndex, numberEndIndex - numberStartIndex) == oldNumberText)
        {
            // 只替换数字部分，保持"．"和其他内容不变
            string before = runText.Substring(0, numberStartIndex);
            string after = runText.Substring(numberEndIndex);
            targetRun.Text = before + newNumberText + after;
        }
    }
    
    /// <summary>
    /// 在多个Run中更新题号，精确处理格式边界
    /// </summary>
    /// <param name="questionInfo">题号信息</param>
    /// <param name="newNumberText">新题号文本</param>
    /// <param name="oldNumberText">旧题号文本</param>
    private void UpdateQuestionNumberInMultipleRuns(QuestionInfo questionInfo, string newNumberText, string oldNumberText)
    {
        var runInfo = questionInfo.QuestionRunInfo;
        var textInfo = runInfo.TextInfo;
        
        // 在完整文本中找到数字部分的精确位置
        string fullText = textInfo.FullText;
        int patternStartIndex = runInfo.FullTextStartIndex;
        
        // 找到数字部分（不包括"．"）
        int numberStartIndex = patternStartIndex;
        int numberEndIndex = patternStartIndex + oldNumberText.Length;
        
        // 验证文本匹配
        if (numberEndIndex <= fullText.Length && 
            fullText.Substring(numberStartIndex, numberEndIndex - numberStartIndex) == oldNumberText)
        {
            // 找到数字跨越的Run并精确替换
            ReplaceTextInMultipleRuns(textInfo, numberStartIndex, numberEndIndex, newNumberText);
        }
    }
    
    /// <summary>
    /// 在多个Run中替换指定范围的文本，保持格式边界
    /// </summary>
    /// <param name="textInfo">段落文本信息</param>
    /// <param name="replaceStartIndex">替换开始位置</param>
    /// <param name="replaceEndIndex">替换结束位置</param>
    /// <param name="newText">新文本</param>
    private void ReplaceTextInMultipleRuns(ParagraphTextInfo textInfo, int replaceStartIndex, int replaceEndIndex, string newText)
    {
        List<Run> runsToModify = new List<Run>();
        List<(Run run, int localStart, int localEnd)> modifications = new List<(Run, int, int)>();
        
        // 找到需要修改的Run和具体位置
        foreach (var (start, end, run) in textInfo.RunMap)
        {
            if (replaceStartIndex < end && replaceEndIndex > start)
            {
                int localStart = Math.Max(0, replaceStartIndex - start);
                int localEnd = Math.Min(end - start, replaceEndIndex - start);
                modifications.Add((run, localStart, localEnd));
            }
        }
        
        if (modifications.Count == 0) return;
        
        // 如果只涉及一个Run，直接处理
        if (modifications.Count == 1)
        {
            var (run, localStart, localEnd) = modifications[0];
            string runText = run.Text;
            string before = runText.Substring(0, localStart);
            string after = runText.Substring(localEnd);
            run.Text = before + newText + after;
        }
        else
        {
            // 多个Run的情况：精确处理每个Run的部分
            for (int i = 0; i < modifications.Count; i++)
            {
                var (run, localStart, localEnd) = modifications[i];
                string runText = run.Text;
                
                if (i == 0)
                {
                    // 第一个Run：保留前面部分，后面部分根据新文本长度决定
                    string before = runText.Substring(0, localStart);
                    int newTextForThisRun = Math.Min(newText.Length, runText.Length - localStart);
                    string newTextPart = newText.Substring(0, newTextForThisRun);
                    run.Text = before + newTextPart;
                }
                else if (i == modifications.Count - 1)
                {
                    // 最后一个Run：保留后面部分
                    string after = runText.Substring(localEnd);
                    int usedNewText = 0;
                    for (int j = 0; j < i; j++)
                    {
                        var (prevRun, _, _) = modifications[j];
                        usedNewText += prevRun.Text.Length - (j == 0 ? modifications[j].localStart : 0);
                    }
                    
                    if (usedNewText < newText.Length)
                    {
                        string remainingNewText = newText.Substring(usedNewText);
                        run.Text = remainingNewText + after;
                    }
                    else
                    {
                        run.Text = after;
                    }
                }
                else
                {
                    // 中间的Run：完全被新文本覆盖或清空
                    run.Text = "";
                }
            }
        }
    }
    
    /// <summary>
    /// 段落文本信息类
    /// </summary>
    private class ParagraphTextInfo
    {
        public string FullText { get; set; } = "";
        public List<(int start, int end, Run run)> RunMap { get; set; } = new List<(int, int, Run)>();
    }
    
    /// <summary>
    /// 题号信息类
    /// </summary>
    private class QuestionInfo
    {
        public Paragraph Paragraph { get; set; } = null!;
        public int OriginalNumber { get; set; }
        public PatternRunInfo QuestionRunInfo { get; set; } = null!;
    }
    
    /// <summary>
    /// 模式Run信息类
    /// </summary>
    private class PatternRunInfo
    {
        public Run TargetRun { get; set; } = null!;
        public List<Run> AffectedRuns { get; set; } = new List<Run>();
        public int StartIndex { get; set; }               // 在主要Run中的开始位置
        public int EndIndex { get; set; }                 // 在主要Run中的结束位置
        public bool IsSingleRun { get; set; }
        public int FullTextStartIndex { get; set; }       // 在完整文本中的开始位置
        public int FullTextEndIndex { get; set; }         // 在完整文本中的结束位置
        public ParagraphTextInfo TextInfo { get; set; } = null!;
    }
} 