using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Math;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;
using System.Text.RegularExpressions;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 设置段落缩进步骤（实现IPipelineStep接口）
/// 功能：只对题号之后、【答案】之前的区域内符合条件的段落设置缩进
/// 题号格式：段落开头的数字+中文点"．"
/// 跳过页眉、页脚、公式、表格和图片段落
/// </summary>
public sealed class SetParagraphIndentStep : IPipelineStep
{
    // 字段声明
    private readonly List<string> _targetCharacters;    // 需要匹配的指定字符列表
    private readonly double _indentValue;              // 缩进值（厘米）
    private readonly Regex _questionPattern;           // 题号匹配正则表达式

    /// <summary>
    /// 构造函数：初始化缩进配置
    /// </summary>
    /// <param name="targetCharacters">需要匹配的段落开头字符列表</param>
    /// <param name="indentValueInCm">段落缩进值（厘米）</param>
    /// <exception cref="ArgumentNullException">目标字符列表为空时抛出</exception>
    /// <exception cref="ArgumentException">缩进值为负时抛出</exception>
    public SetParagraphIndentStep(List<string> targetCharacters, double indentValueInCm)
    {
        // 参数有效性验证
        if (targetCharacters == null || targetCharacters.Count == 0)
            throw new ArgumentNullException(nameof(targetCharacters), "目标字符列表不能为空");

        _targetCharacters = targetCharacters.Where(c => !string.IsNullOrWhiteSpace(c))
                                          .Select(c => c.Trim())
                                          .ToList();

        if (_targetCharacters.Count == 0)
            throw new ArgumentException("至少需要一个有效的目标字符", nameof(targetCharacters));

        _indentValue = indentValueInCm >= 0 ? UnitConverter.CmToPoints(indentValueInCm)
            : throw new ArgumentException("缩进值不能为负数", nameof(indentValueInCm));
        
        // 初始化题号匹配正则表达式：严格匹配段落开头的数字+中文点"．"
        _questionPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取所有段落节点并转换为列表
            NodeCollection paragraphNodes = doc.GetChildNodes(NodeType.Paragraph, true);
            List<Paragraph> paragraphs = paragraphNodes.Cast<Paragraph>()
                .Where(p => !IsInHeaderFooter(p) && !IsInTable(p))
                .ToList();

            // 识别题目区域
            var questionRegions = IdentifyQuestionRegions(paragraphs);

            // 遍历处理每个段落
            foreach (Paragraph paragraph in paragraphs)
            {
                // 跳过需要忽略的段落
                if (ShouldSkipParagraph(paragraph)) continue;

                // 检查段落是否在题目区域内（题号之后、【答案】之前）
                if (!IsInQuestionRegion(paragraph, questionRegions)) continue;

                // 检查段落开头是否匹配目标字符
                if (StartsWithTargetCharacter(paragraph))
                {
                    // 设置段落缩进
                    SetParagraphIndent(paragraph);
                }
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 识别题目区域（题号之后、【答案】之前）
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <returns>题目区域列表</returns>
    private List<QuestionRegion> IdentifyQuestionRegions(List<Paragraph> paragraphs)
    {
        var regions = new List<QuestionRegion>();
        
        for (int i = 0; i < paragraphs.Count; i++)
        {
            // 检查是否为题号段落
            if (IsQuestionParagraph(paragraphs[i]))
            {
                var region = new QuestionRegion
                {
                    QuestionIndex = i,
                    ContentStartIndex = i + 1,  // 题目内容从题号后一段开始
                    AnswerIndex = -1
                };
                
                // 查找对应的【答案】段落
                for (int j = i + 1; j < paragraphs.Count; j++)
                {
                    string text = BuildParagraphTextInfo(paragraphs[j]).FullText.Trim();
                    
                    // 如果遇到【答案】标记
                    if (text.Contains("【答案】"))
                    {
                        region.AnswerIndex = j;
                        break;
                    }
                    
                    // 如果遇到下一个题号，停止查找
                    if (j > i && IsQuestionParagraph(paragraphs[j]))
                    {
                        break;
                    }
                }
                
                regions.Add(region);
            }
        }
        
        return regions;
    }
    
    /// <summary>
    /// 检查段落是否为题号段落（处理跨Run）
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否为题号段落</returns>
    private bool IsQuestionParagraph(Paragraph paragraph)
    {
        // 构建段落文本信息
        ParagraphTextInfo textInfo = BuildParagraphTextInfo(paragraph);
        
        // 获取段落开头的文本（去除前导空白）
        string paragraphText = textInfo.FullText.TrimStart();
        
        // 使用正则表达式匹配题号格式
        return _questionPattern.IsMatch(paragraphText);
    }
    
    /// <summary>
    /// 检查段落是否在题目区域内
    /// </summary>
    /// <param name="paragraph">待检查的段落</param>
    /// <param name="questionRegions">题目区域列表</param>
    /// <returns>是否在题目区域内</returns>
    private bool IsInQuestionRegion(Paragraph paragraph, List<QuestionRegion> questionRegions)
    {
        // 获取过滤后的段落列表来确定当前段落的位置
        NodeCollection paragraphNodes = paragraph.Document.GetChildNodes(NodeType.Paragraph, true);
        List<Paragraph> filteredParagraphs = paragraphNodes.Cast<Paragraph>()
            .Where(p => !IsInHeaderFooter(p) && !IsInTable(p))
            .ToList();
        
        int currentIndex = filteredParagraphs.IndexOf(paragraph);
        if (currentIndex == -1) return false;
        
        // 检查是否在任何题目区域内
        foreach (var region in questionRegions)
        {
            // 如果该题目有答案段落，检查是否在题目内容区域内
            if (region.AnswerIndex >= 0)
            {
                if (currentIndex >= region.ContentStartIndex && currentIndex < region.AnswerIndex)
                {
                    return true;
                }
            }
            // 如果该题目没有答案段落，检查是否在题目内容开始之后且在下一个题目之前
            else
            {
                if (currentIndex >= region.ContentStartIndex)
                {
                    // 查找下一个题目的开始位置
                    var nextRegion = questionRegions.FirstOrDefault(r => r.QuestionIndex > region.QuestionIndex);
                    if (nextRegion != null)
                    {
                        if (currentIndex < nextRegion.QuestionIndex)
                        {
                            return true;
                        }
                    }
                    else
                    {
                        // 这是最后一个题目，无下一个题目
                        return true;
                    }
                }
            }
        }
        
        return false;
    }

    /// <summary>
    /// 判断是否应跳过段落处理
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否跳过</returns>
    private bool ShouldSkipParagraph(Paragraph paragraph)
    {
        // 跳过页眉页脚内容（完全跳过）
        if (IsInHeaderFooter(paragraph))
            return true;

        // 跳过表格内容（完全跳过）
        if (IsInTable(paragraph))
            return true;

        // 只有当段落开头是公式时才跳过
        if (StartsWithOfficeMath(paragraph))
            return true;

        // 只有当段落开头是图片时才跳过
        if (StartsWithImage(paragraph))
            return true;

        return false;
    }

    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    /// <param name="node">待检查的节点</param>
    /// <returns>是否在页眉页脚中</returns>
    private static bool IsInHeaderFooter(Node node)
    {
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 检查段落是否位于表格中
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否在表格中</returns>
    private static bool IsInTable(Paragraph paragraph)
    {
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
                return true;
            parent = parent.ParentNode;
        }
        return false;
    }

    /// <summary>
    /// 检查段落是否以公式开头
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否以公式开头</returns>
    private static bool StartsWithOfficeMath(Paragraph paragraph)
    {
        // 遍历段落的子节点，找到第一个非空白内容
        foreach (Node child in paragraph.GetChildNodes(NodeType.Any, false))
        {
            // 如果遇到公式节点且前面没有文本内容，则认为以公式开头
            if (child is OfficeMath)
                return true;
                
            // 如果遇到Run节点
            if (child is Run run)
            {
                // 如果这个Run在公式中，且前面没有文本内容，则认为以公式开头
                if (run.GetAncestor(typeof(OfficeMath)) != null)
                    return true;
                    
                // 如果这个Run有实际文本内容，则不是以公式开头
                if (!string.IsNullOrWhiteSpace(run.Text))
                    return false;
            }
            
            // 如果遇到其他有内容的节点，则不是以公式开头
            if (child.NodeType != NodeType.Run && child.NodeType != NodeType.OfficeMath)
            {
                string nodeText = child.ToString(SaveFormat.Text);
                if (!string.IsNullOrWhiteSpace(nodeText))
                    return false;
            }
        }
        
        return false;
    }

    /// <summary>
    /// 检查段落是否以图片开头
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否以图片开头</returns>
    private static bool StartsWithImage(Paragraph paragraph)
    {
        // 遍历段落的子节点，找到第一个非空白内容
        foreach (Node child in paragraph.GetChildNodes(NodeType.Any, false))
        {
            // 如果遇到Shape节点且包含图片，且前面没有文本内容，则认为以图片开头
            if (child is Shape shape && shape.ImageData.HasImage)
                return true;
                
            // 如果遇到Run节点
            if (child is Run run)
            {
                // 如果这个Run有实际文本内容，则不是以图片开头
                if (!string.IsNullOrWhiteSpace(run.Text))
                    return false;
            }
            
            // 如果遇到其他有内容的节点，则不是以图片开头
            if (child.NodeType != NodeType.Run && child.NodeType != NodeType.Shape)
            {
                string nodeText = child.ToString(SaveFormat.Text);
                if (!string.IsNullOrWhiteSpace(nodeText))
                    return false;
            }
        }
        
        return false;
    }

    /// <summary>
    /// 检查段落开头是否与目标字符匹配（处理跨Run问题）
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否匹配</returns>
    private bool StartsWithTargetCharacter(Paragraph paragraph)
    {
        // 构建段落文本信息
        ParagraphTextInfo textInfo = BuildParagraphTextInfo(paragraph);
        
        // 获取段落开头的文本（去除前导空白）
        string paragraphText = textInfo.FullText.TrimStart();
        
        // 检查是否以任何目标字符开头（严格匹配，区分大小写）
        return _targetCharacters.Any(target => paragraphText.StartsWith(target, StringComparison.Ordinal));
    }

    /// <summary>
    /// 构建段落文本信息（处理跨Run匹配）
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>段落文本信息</returns>
    private ParagraphTextInfo BuildParagraphTextInfo(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            // 跳过公式中的Run
            if (IsInOfficeMath(run)) continue;
            
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        return new ParagraphTextInfo
        {
            FullText = fullText,
            RunMap = runMap
        };
    }

    /// <summary>
    /// 检查Run是否位于公式中
    /// </summary>
    /// <param name="run">Run节点</param>
    /// <returns>是否在公式中</returns>
    private static bool IsInOfficeMath(Run run)
    {
        return run.GetAncestor(typeof(OfficeMath)) != null;
    }

    /// <summary>
    /// 设置段落缩进
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    private void SetParagraphIndent(Paragraph paragraph)
    {
        // 设置左缩进（首行缩进和左缩进都设置为指定值）
        paragraph.ParagraphFormat.LeftIndent = _indentValue;
        paragraph.ParagraphFormat.FirstLineIndent = 0; // 首行缩进设置为0，避免双重缩进
    }

    /// <summary>
    /// 段落文本信息类
    /// </summary>
    private class ParagraphTextInfo
    {
        public string FullText { get; set; } = "";
        public List<(int start, int end, Run run)> RunMap { get; set; } = new List<(int, int, Run)>();
    }
    
    /// <summary>
    /// 题目区域信息类
    /// </summary>
    private class QuestionRegion
    {
        public int QuestionIndex { get; set; }      // 题号段落索引
        public int ContentStartIndex { get; set; }  // 题目内容开始索引
        public int AnswerIndex { get; set; }        // 答案段落索引（-1表示未找到）
    }
}
