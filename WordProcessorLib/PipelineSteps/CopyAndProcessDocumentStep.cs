using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib;

/// <summary>
/// 文档处理类：复制文档并处理答案部分
/// 功能：
/// 1. 在文档末尾添加分节符
/// 2. 复制原始文档内容并粘贴到分节符后
/// 3. 仅处理第一部分内容，第二部分保持原样
/// </summary>
public class CopyAndProcessDocumentStep : IPipelineStep
{
    // 存储不同END标记对应的空行数
    private readonly Dictionary<string, int> _emptyLineCount;

    /// <summary>
    /// 构造函数，初始化各END标记对应的空行数
    /// </summary>
    /// <param name="end1Lines">【END1】标记处插入的空行数</param>
    /// <param name="end2Lines">【END2】标记处插入的空行数</param>
    /// <param name="end3Lines">【END3】标记处插入的空行数</param>
    /// <param name="end4Lines">【END4】标记处插入的空行数</param>
    public CopyAndProcessDocumentStep(int end1Lines, int end2Lines, int end3Lines, int end4Lines)
    {
        _emptyLineCount = new Dictionary<string, int>
        {
            { "【END1】", end1Lines },
            { "【END2】", end2Lines },
            { "【END3】", end3Lines },
            { "【END4】", end4Lines }
        };
    }

    /// <summary>
    /// 执行文档处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>是否处理成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 1. 保存原始文档副本（用于后续追加）
            Document docCopy = (Document)doc.Clone(true);
            
            // 2. 处理原文档中的答案部分（处理第一部分）
            ProcessFirstPart(doc);
            
            // 3. 在原文档末尾添加分节符，会导致出现两个分节符，所以不插入分节符
            DocumentBuilder builder = new DocumentBuilder(doc);
            builder.MoveToDocumentEnd();
            // builder.InsertBreak(BreakType.SectionBreakNewPage);
            
            // 4. 将副本添加到原文档末尾（第二部分保持原样）
            doc.AppendDocument(docCopy, ImportFormatMode.KeepSourceFormatting);
            
            // ================= 新增：处理第一个section末尾的空段落 =================
            // 清理第一个section末尾的多余空行，只保留一个空段落
            CleanupFirstSectionEndSpaces(doc);
            
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理文件 {filePath} 失败: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 处理文档第一部分：删除答案段落并添加空行
    /// </summary>
    /// <param name="doc">待处理的文档</param>
    private void ProcessFirstPart(Document doc)
    {
        // 获取所有段落
        List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        // 找出所有答案段落及其对应的END标记
        Dictionary<int, Tuple<Paragraph, Paragraph, string>> answerEndPairs = new Dictionary<int, Tuple<Paragraph, Paragraph, string>>();
        
        // 先找出所有答案段落
        for (int i = 0; i < allParagraphs.Count; i++)
        {
            Paragraph para = allParagraphs[i];
            string text = para.GetText();
            
            if (text.Contains("【答案】"))
            {
                // 查找对应的END标记
                for (int j = i + 1; j < allParagraphs.Count; j++)
                {
                    Paragraph endPara = allParagraphs[j];
                    string endText = endPara.GetText();
                    
                    foreach (string endMarker in _emptyLineCount.Keys)
                    {
                        if (endText.Contains(endMarker))
                        {
                            answerEndPairs[i] = new Tuple<Paragraph, Paragraph, string>(para, endPara, endMarker);
                            break;
                        }
                    }
                    
                    if (answerEndPairs.ContainsKey(i))
                        break;
                }
            }
        }
        
        // 从后向前处理每对答案和END标记（防止索引变化影响）
        foreach (var pair in answerEndPairs.OrderByDescending(p => p.Key))
        {
            Paragraph answerPara = pair.Value.Item1;
            Paragraph endPara = pair.Value.Item2;
            string endMarker = pair.Value.Item3;
            
            // 处理这对答案和END标记
            ProcessAnswerSection(doc, answerPara, endPara, _emptyLineCount[endMarker]);
        }
    }

    /// <summary>
    /// 处理单个答案区域：删除段落并插入空行
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="answerParagraph">答案段落</param>
    /// <param name="endParagraph">END标记段落</param>
    /// <param name="emptyLineCount">要插入的空行数</param>
    private void ProcessAnswerSection(Document doc, Paragraph answerParagraph, Paragraph endParagraph, int emptyLineCount)
    {
        // 检查答案段落是否在表格内
        if (IsInsideTable(answerParagraph))
        {
            // 特殊处理表格内的答案，删除整个表格
            ProcessAnswerInTable(answerParagraph, endParagraph);
            return;
        }
        
        // 找到答案段落前一个非空段落
        Paragraph prevContentParagraph = FindPreviousContentParagraph(answerParagraph);
        
        // 找到END标记后一个非空段落
        Paragraph nextContentParagraph = FindNextContentParagraph(endParagraph);
        
        // 收集从答案到END之间的所有段落（包括答案和END）
        List<Paragraph> paragraphsToRemove = new List<Paragraph>();
        bool collecting = false;
        
        foreach (Paragraph para in doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>())
        {
            if (para == answerParagraph)
            {
                collecting = true;
            }
            
            if (collecting)
            {
                paragraphsToRemove.Add(para);
            }
            
            if (para == endParagraph)
            {
                collecting = false;
            }
        }
        
        // 删除收集到的段落
        foreach (Paragraph para in paragraphsToRemove)
        {
            para.Remove();
        }
        
        // ===================== 新增：删除答案区域内的空表格 =====================
        // 删除段落后，检查并删除【答案】和【END】之间因段落删除而变空的表格
        RemoveEmptyTablesInAnswerArea(doc, prevContentParagraph, nextContentParagraph);
        
        // 清除答案段落前和END标记后之间的空行
        if (prevContentParagraph != null && nextContentParagraph != null)
        {
            ClearEmptyLinesBetween(doc, prevContentParagraph, nextContentParagraph);
        }
        
        // 插入指定数量的空行
        InsertEmptyLines(doc, prevContentParagraph, nextContentParagraph, emptyLineCount);
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    /// <param name="paragraph">要检查的段落</param>
    /// <returns>是否在表格内</returns>
    private bool IsInsideTable(Paragraph paragraph)
    {
        // 遍历段落的父节点，检查是否有表格单元格
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        
        return false;
    }

    /// <summary>
    /// 处理表格内的答案段落：删除整个表格
    /// </summary>
    /// <param name="answerParagraph">答案段落</param>
    /// <param name="endParagraph">END标记段落</param>
    private void ProcessAnswerInTable(Paragraph answerParagraph, Paragraph endParagraph)
    {
        // 找到答案段落所在的表格
        Table answerTable = GetParentTable(answerParagraph);
        if (answerTable == null) return;
        
        // 检查END段落是否也在同一个表格内
        Table endTable = GetParentTable(endParagraph);
        
        if (answerTable == endTable)
        {
            // 【答案】和【END】在同一个表格内，删除整个表格
            answerTable.Remove();
        }
        else
        {
            // 【答案】和【END】在不同表格内，只删除答案所在的表格
            // 这种情况下，END可能在表格外的段落中
            answerTable.Remove();
        }
    }

    /// <summary>
    /// 获取段落所在的表格
    /// </summary>
    /// <param name="paragraph">段落</param>
    /// <returns>表格对象，如果不在表格内则返回null</returns>
    private Table GetParentTable(Paragraph paragraph)
    {
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Table)
            {
                return (Table)parent;
            }
            parent = parent.ParentNode;
        }
        
        return null;
    }

    /// <summary>
    /// 获取段落所在的单元格
    /// </summary>
    /// <param name="paragraph">段落</param>
    /// <returns>单元格对象，如果不在单元格内则返回null</returns>
    private Cell GetParentCell(Paragraph paragraph)
    {
        Node parent = paragraph.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return (Cell)parent;
            }
            parent = parent.ParentNode;
        }
        
        return null;
    }

    /// <summary>
    /// 查找指定段落前面的非空段落
    /// </summary>
    /// <param name="paragraph">当前段落</param>
    /// <returns>前一个非空段落</returns>
    private Paragraph FindPreviousContentParagraph(Paragraph paragraph)
    {
        // 获取文档中的所有段落
        List<Paragraph> allParagraphs = paragraph.Document.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        // 找到当前段落的索引
        int currentIndex = allParagraphs.IndexOf(paragraph);
        if (currentIndex <= 0)
        {
            return null;
        }
        
        // 向前查找非空段落
        for (int i = currentIndex - 1; i >= 0; i--)
        {
            Paragraph prevPara = allParagraphs[i];
            string text = prevPara.GetText().Trim();
            
            // 如果段落在表格内，并且当前段落也在表格内，需要确保在同一个单元格内
            if (IsInsideTable(paragraph) && IsInsideTable(prevPara))
            {
                Cell currentCell = GetParentCell(paragraph);
                Cell prevCell = GetParentCell(prevPara);
                
                if (currentCell != prevCell)
                {
                    continue; // 不在同一个单元格内，跳过
                }
            }
            
            if (!string.IsNullOrWhiteSpace(text))
            {
                return prevPara;
            }
        }
        
        return null;
    }

    /// <summary>
    /// 查找指定段落后面的非空段落
    /// </summary>
    /// <param name="paragraph">当前段落</param>
    /// <returns>后一个非空段落</returns>
    private Paragraph FindNextContentParagraph(Paragraph paragraph)
    {
        // 获取文档中的所有段落
        List<Paragraph> allParagraphs = paragraph.Document.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        // 找到当前段落的索引
        int currentIndex = allParagraphs.IndexOf(paragraph);
        if (currentIndex < 0 || currentIndex >= allParagraphs.Count - 1)
        {
            return null;
        }
        
        // 向后查找非空段落
        for (int i = currentIndex + 1; i < allParagraphs.Count; i++)
        {
            Paragraph nextPara = allParagraphs[i];
            string text = nextPara.GetText().Trim();
            
            if (!string.IsNullOrWhiteSpace(text))
            {
                return nextPara;
            }
        }
        
        return null;
    }

    /// <summary>
    /// 清除两个段落之间的所有空行
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="prevParagraph">前一个段落</param>
    /// <param name="nextParagraph">后一个段落</param>
    private void ClearEmptyLinesBetween(Document doc, Paragraph prevParagraph, Paragraph nextParagraph)
    {
        if (prevParagraph == null || nextParagraph == null)
        {
            return;
        }
        
        // 获取文档中的所有段落
        List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        // 找到前后段落的索引
        int prevIndex = allParagraphs.IndexOf(prevParagraph);
        int nextIndex = allParagraphs.IndexOf(nextParagraph);
        
        if (prevIndex < 0 || nextIndex < 0 || prevIndex >= nextIndex - 1)
        {
            return; // 索引无效，或两段落相邻
        }
        
        // 收集两段落之间的空段落
        List<Paragraph> emptyParagraphs = new List<Paragraph>();
        for (int i = prevIndex + 1; i < nextIndex; i++)
        {
            Paragraph para = allParagraphs[i];
            //if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
            if (IsParagraphEmpty(para))       // 误删图片修正部分    
            {
                emptyParagraphs.Add(para);
            }
        }
        
        // 删除空段落
        foreach (Paragraph para in emptyParagraphs)
        {
            para.Remove();
        }
    }

    // ================ 修改部分 =================
    /// <summary>
    /// 判断段落是否为空（无文本且无图片）
    /// </summary>
    private bool IsParagraphEmpty(Paragraph para)
    {
        // 检查是否有非空白文本
        if (!string.IsNullOrWhiteSpace(para.GetText().Trim()))
            return false;
    
        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    private bool HasImageOrShape(Paragraph para)
    {
        // 直接搜索段落中的 Shape 节点（Aspose.Words.Drawing.Shape）
        foreach (Shape shape in para.GetChildNodes(NodeType.Shape, true))
        {
            if (shape.ImageData.HasImage)
                return true;
        }
        return false;
    }
    // ================ 修改部分 =================
    
    /// <summary>
    /// 在两个段落之间插入指定数量的空行
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="prevParagraph">前一个段落</param>
    /// <param name="nextParagraph">后一个段落</param>
    /// <param name="emptyLineCount">空行数量</param>
    private void InsertEmptyLines(Document doc, Paragraph prevParagraph, Paragraph nextParagraph, int emptyLineCount)
    {
        // 获取前一段最后一个Run的字体大小，用于设置空行的字体大小
        double fontSize = GetLastRunFontSize(prevParagraph);
        
        if (nextParagraph != null && prevParagraph != null)
        {
            // 在上下段落之间插入空行
            for (int i = 0; i < emptyLineCount; i++)
            {
                Paragraph emptyPara = new Paragraph(doc);
                SetEmptyParagraphFont(emptyPara, fontSize);
                nextParagraph.ParentNode.InsertBefore(emptyPara, nextParagraph);
            }
        }
        else if (prevParagraph != null)
        {
            // 只有上方段落存在（答案区域在文档末尾）
            Node insertPoint = prevParagraph;
            
            for (int i = 0; i < emptyLineCount; i++)
            {
                Paragraph emptyPara = new Paragraph(doc);
                SetEmptyParagraphFont(emptyPara, fontSize);
                insertPoint.ParentNode.InsertAfter(emptyPara, insertPoint);
                insertPoint = emptyPara; // 更新插入点，确保后续段落插入在正确位置
            }
        }
        else if (nextParagraph != null)
        {
            // 只有下方段落存在（答案区域在文档开头）
            for (int i = 0; i < emptyLineCount; i++)
            {
                Paragraph emptyPara = new Paragraph(doc);
                SetEmptyParagraphFont(emptyPara, fontSize);
                nextParagraph.ParentNode.InsertBefore(emptyPara, nextParagraph);
            }
        }
    }

    /// <summary>
    /// 删除答案区域内的空表格
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="prevParagraph">答案段落前面的非空段落</param>
    /// <param name="nextParagraph">答案段落后面的非空段落</param>
    private void RemoveEmptyTablesInAnswerArea(Document doc, Paragraph prevParagraph, Paragraph nextParagraph)
    {
        if (prevParagraph == null || nextParagraph == null)
        {
            return;
        }
        
        // 获取文档中的所有段落，用于定位答案区域范围
        List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        // 找到前后段落的索引
        int prevIndex = allParagraphs.IndexOf(prevParagraph);
        int nextIndex = allParagraphs.IndexOf(nextParagraph);
        
        if (prevIndex < 0 || nextIndex < 0 || prevIndex >= nextIndex - 1)
        {
            return; // 索引无效，或两段落相邻
        }
        
        // 获取文档中的所有表格
        List<Table> allTables = doc.GetChildNodes(NodeType.Table, true).Cast<Table>().ToList();
        
        // 收集位于答案区域内的表格
        List<Table> tablesInAnswerArea = new List<Table>();
        
        foreach (Table table in allTables)
        {
            // 检查表格是否在答案区域内
            if (IsTableInAnswerArea(table, prevParagraph, nextParagraph, allParagraphs, prevIndex, nextIndex))
            {
                tablesInAnswerArea.Add(table);
            }
        }
        
        // 删除答案区域内的所有表格（无论是否为空，因为它们都在答案区域内）
        foreach (Table table in tablesInAnswerArea)
        {
            table.Remove();
        }
    }

    /// <summary>
    /// 判断表格是否在答案区域内
    /// </summary>
    /// <param name="table">要检查的表格</param>
    /// <param name="prevParagraph">答案前段落</param>
    /// <param name="nextParagraph">答案后段落</param>
    /// <param name="allParagraphs">所有段落列表</param>
    /// <param name="prevIndex">答案前段落索引</param>
    /// <param name="nextIndex">答案后段落索引</param>
    /// <returns>是否在答案区域内</returns>
    private bool IsTableInAnswerArea(Table table, Paragraph prevParagraph, Paragraph nextParagraph, 
        List<Paragraph> allParagraphs, int prevIndex, int nextIndex)
    {
        // 检查表格的位置是否在答案区域范围内
        // 通过比较表格在文档中的位置与答案前后段落的位置来判断
        
        // 获取表格的前一个段落
        Node tablePrevNode = table.PreviousSibling;
        while (tablePrevNode != null && !(tablePrevNode is Paragraph))
        {
            tablePrevNode = tablePrevNode.PreviousSibling;
        }
        
        // 获取表格的后一个段落
        Node tableNextNode = table.NextSibling;
        while (tableNextNode != null && !(tableNextNode is Paragraph))
        {
            tableNextNode = tableNextNode.NextSibling;
        }
        
        // 如果表格前后都有段落，检查这些段落是否在答案区域内
        if (tablePrevNode is Paragraph tablePrev && tableNextNode is Paragraph tableNext)
        {
            int tablePrevIndex = allParagraphs.IndexOf(tablePrev);
            int tableNextIndex = allParagraphs.IndexOf(tableNext);
            
            // 表格在答案区域内的条件：表格前的段落在prevIndex之后，表格后的段落在nextIndex之前
            return tablePrevIndex > prevIndex && tableNextIndex < nextIndex;
        }
        
        return false;
    }

    /// <summary>
    /// 判断表格是否为空
    /// </summary>
    /// <param name="table">要检查的表格</param>
    /// <returns>是否为空</returns>
    private bool IsTableEmpty(Table table)
    {
        // 遍历表格中的所有单元格，检查是否有非空文本或图片
        foreach (Cell cell in table.GetChildNodes(NodeType.Cell, true).Cast<Cell>())
        {
            string cellText = cell.GetText().Trim();
            if (!string.IsNullOrWhiteSpace(cellText))
            {
                return false;
            }
            
            // 检查单元格中是否有图片
            if (cell.GetChildNodes(NodeType.Shape, true).Count > 0)
            {
                return false;
            }
        }
        return true;
    }

    /// <summary>
    /// 清理第一个section末尾的多余空行，只保留一个空段落
    /// </summary>
    /// <param name="doc">文档对象</param>
    private void CleanupFirstSectionEndSpaces(Document doc)
    {
        // 获取第一个section（题目部分）
        if (doc.Sections.Count == 0) return;
        
        Section firstSection = doc.Sections[0];
        
        // 获取第一个section中的所有段落
        List<Paragraph> sectionParagraphs = firstSection.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        
        if (sectionParagraphs.Count == 0) return;
        
        // 从最后一个段落开始向前查找，收集末尾的连续空段落
        List<Paragraph> trailingEmptyParagraphs = new List<Paragraph>();
        
        for (int i = sectionParagraphs.Count - 1; i >= 0; i--)
        {
            Paragraph para = sectionParagraphs[i];
            
            // 判断段落是否为空
            if (IsParagraphEmpty(para))
            {
                trailingEmptyParagraphs.Add(para);
            }
            else
            {
                break; // 遇到非空段落就停止
            }
        }
        
        // 获取前一段最后一个Run的字体大小，用于设置空行的字体大小
        double fontSize = 12.0; // 默认字体大小
        if (sectionParagraphs.Count > trailingEmptyParagraphs.Count)
        {
            // 找到最后一个非空段落
            Paragraph lastContentPara = sectionParagraphs[sectionParagraphs.Count - trailingEmptyParagraphs.Count - 1];
            fontSize = GetLastRunFontSize(lastContentPara);
        }
        
        // 如果末尾的空段落数量大于1，则删除多余的，只保留1个
        if (trailingEmptyParagraphs.Count > 1)
        {
            // 删除多余的空段落，保留最后一个（即trailingEmptyParagraphs的第一个）
            for (int i = 1; i < trailingEmptyParagraphs.Count; i++)
            {
                trailingEmptyParagraphs[i].Remove();
            }
            // 设置保留的空段落的字体
            SetEmptyParagraphFont(trailingEmptyParagraphs[0], fontSize);
        }
        // 如果末尾没有空段落，添加一个空段落
        else if (trailingEmptyParagraphs.Count == 0)
        {
            Paragraph emptyPara = new Paragraph(doc);
            SetEmptyParagraphFont(emptyPara, fontSize);
            emptyPara.AppendChild(new Run(doc, ""));
            firstSection.Body.AppendChild(emptyPara);
        }
        // 如果只有一个空段落，设置其字体
        else if (trailingEmptyParagraphs.Count == 1)
        {
            SetEmptyParagraphFont(trailingEmptyParagraphs[0], fontSize);
        }
    }

    /// <summary>
    /// 获取段落最后一个Run的字体大小
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <returns>字体大小，如果无法获取则返回默认值12.0</returns>
    private double GetLastRunFontSize(Paragraph paragraph)
    {
        if (paragraph == null)
            return 12.0; // 默认字体大小
        
        // 获取段落中的所有Run节点
        var runs = paragraph.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();
        
        if (runs.Count > 0)
        {
            // 返回最后一个Run的字体大小
            return runs.Last().Font.Size;
        }
        
        return 12.0; // 默认字体大小
    }

    /// <summary>
    /// 设置空段落的字体格式
    /// </summary>
    /// <param name="paragraph">空段落对象</param>
    /// <param name="fontSize">字体大小</param>
    private void SetEmptyParagraphFont(Paragraph paragraph, double fontSize)
    {
        if (paragraph == null) return;
        
        // 设置段落换行符的字体格式
        paragraph.ParagraphBreakFont.NameFarEast = "宋体";
        paragraph.ParagraphBreakFont.Name = "XITS Math";
        paragraph.ParagraphBreakFont.Bold = false;
        paragraph.ParagraphBreakFont.Size = fontSize;
    }
}