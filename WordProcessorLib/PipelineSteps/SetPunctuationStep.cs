using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// ============================================ 设置标点符号步骤 ============================================
// 作用：对文档的一些中文标点和个别特殊字符的进行格式化（跳过公式，因为已经在公式标准化步骤中分别设置了中文和西文的字体和斜体）
// 字体替换处理步骤（实现IPipelineStep接口）
// 功能：通过构造函数接收中西文字体配置，独立替换文档字体
public class SetPunctuationStep : IPipelineStep
{
    // 需要处理的标点集合（可根据需求扩展）
    private static readonly HashSet<char> TargetPunctuation = new HashSet<char> 
    { 
        '“', '”', '‘', '’', '—', '…', '·', '（', '）', 'φ', '·',
        '①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩',
        '★', '☆',
    };

    private readonly string _latinFont;         // 西文字体配置
    private readonly string _cjkFont;           // 中文字体配置，不能为空

    // 构造函数：初始化字体配置
    // mathCjkFont：公式中的中文字体名称，因为要分拆Run，所以不能为空。替换公式中的中文字体
    public SetPunctuationStep(string? latinFont, string? cjkFont)
    {
        _latinFont = latinFont;
        _cjkFont = cjkFont;
    }

    // 主执行方法：文档处理入口
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            ProcessDocument(doc);
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
    
    private void ProcessDocument(Document doc)
    {
        // 遍历所有段落（跳过页眉页脚）
        foreach (Paragraph para in doc.GetChildNodes(NodeType.Paragraph, true))
        {
            if (IsInHeaderFooter(para)) continue;

            // 遍历段落中的Run节点
            foreach (Run run in para.GetChildNodes(NodeType.Run, true))
            {
                if (ShouldSkipNode(run)) continue;
                ProcessRun(run);
            }
        }
    }
    
    // 判断是否需要跳过节点处理
    private bool ShouldSkipNode(Node node)
    {
        // 跳过公式节点
        if (IsInOfficeMath(node)) return true;

        // 跳过空文本节点
        return string.IsNullOrEmpty((node as Run)?.Text);
    }

    // 检查节点是否位于页眉/页脚区域
    // node：当前文档节点
    // true：属于页眉页脚内容
    // false：属于正文内容
    private static bool IsInHeaderFooter(Node node)
    {
        // 通过查找最近的HeaderFooter祖先节点判断位置
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    // 检查是否属于公式节点
    private static bool IsInOfficeMath(Node node)
    {
        while (node != null)
        {
            if (node is OfficeMath) return true;
            node = node.ParentNode;
        }
        return false;
    }
    
    // 处理单个Run节点
    private void ProcessRun(Run originalRun)
    {
        string text = originalRun.Text;
        List<int> targetIndexes = new List<int>();

        // 记录所有目标标点的位置
        for (int i = 0; i < text.Length; i++)
        {
            if (TargetPunctuation.Contains(text[i]))
                targetIndexes.Add(i);
        }

        if (targetIndexes.Count == 0) return;

        // 处理纯标点Run
        if (text.Length == 1)
        {
            if (text == "φ")
                ApplyLatinStyle(originalRun);
            else
                ApplyChineseStyle(originalRun);
            return;
        }

        // 拆分处理多个字符的Run
        SplitRun(originalRun, text, targetIndexes);
    }
    
    // 拆分Run并应用格式
    private void SplitRun(Run originalRun, string text, List<int> splitIndexes)
    {
        CompositeNode parent = originalRun.ParentNode;
        int currentPos = 0;

        foreach (int index in splitIndexes)
        {
            // 处理前导文本（如果有）
            if (index > currentPos)
            {
                Run leadingRun = CloneRun(originalRun, text.Substring(currentPos, index - currentPos));
                parent.InsertBefore(leadingRun, originalRun);
            }

            // 处理目标标点
            Run punctuationRun = CloneRun(originalRun, text[index].ToString());
            if (text[index].ToString() == "φ")
                ApplyLatinStyle(punctuationRun);
            else
                ApplyChineseStyle(punctuationRun);
            parent.InsertBefore(punctuationRun, originalRun);

            currentPos = index + 1;
        }

        // 处理剩余文本（如果有）
        if (currentPos < text.Length)
        {
            Run trailingRun = CloneRun(originalRun, text.Substring(currentPos));
            parent.InsertBefore(trailingRun, originalRun);
        }

        // 移除原始Run
        originalRun.Remove();
    }
    
    // 克隆Run并设置新文本
    private Run CloneRun(Run sourceRun, string newText)
    {
        Run clonedRun = (Run)sourceRun.Clone(true);
        clonedRun.Text = newText;
        return clonedRun;
    }

    // 应用中文标点样式
    private void ApplyChineseStyle(Run run)
    {
        run.Font.Name = _cjkFont;
        run.Font.NameFarEast = _cjkFont;
        run.Font.Italic = false;
    }
    
    // 应用西文标点样式
    private void ApplyLatinStyle(Run run)
    {
        run.Font.Name = _latinFont;
        //run.Font.NameFarEast = _latinFont;
        run.Font.Italic = true;
    }
    
    
    // private bool IsChineseCharacter(char c)
    // {
    //     // 检查是否在中文标点符号范围内
    //     return 
    //         c == '\u201C' ||        // “
    //         c == '\u201D' ||        // ”
    //         c == '\u2018' ||        // ‘
    //         c == '\u2019' ||        // ’
    //         c == '\u2014' ||        // —
    //         c == '\u2026' ||        // …
    //         c == '\u00B7' ||        // ·
    //         c == '\uFF08' ||        // （
    //         c == '\uFF09';          // ）
    // }
}