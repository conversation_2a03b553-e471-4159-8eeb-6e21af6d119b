using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Math;
using Aspose.Words.Replacing;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 文本转微软公式步骤（实现IPipelineStep接口）
/// 功能：智能识别数学表达式文本并转换为微软公式格式
/// 注意：此为测试功能，目前完全无法使用
/// </summary>
public sealed class ConvertTextToMathStep : IPipelineStep
{
    // 数学运算符集合
    private static readonly HashSet<char> MathOperators = new HashSet<char>
    {
        '+', '-', '*', '/', '=', '<', '>', '≤', '≥', '≠', '≈', '±', '∓',
        '×', '÷', '∑', '∏', '∫', '√', '∞', '∂', '∆', '∇', '∈', '∉',
        '⊂', '⊃', '∩', '∪', '∅', '∀', '∃', '→', '←', '↔', '⇒', '⇐', '⇔'
    };

    // 允许的字符集合（小写字母、数字、数学运算符、中英文括号）
    private static readonly HashSet<char> AllowedChars = new HashSet<char>();

    // 常见单位词汇（不应转换为公式）
    private static readonly HashSet<string> UnitWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "m", "cm", "mm", "km", "dm", "μm", "nm", "pm",           // 长度单位
        "g", "kg", "mg", "μg", "t", "lb", "oz",                   // 质量单位
        "s", "min", "h", "ms", "μs", "ns",                       // 时间单位
        "A", "mA", "μA", "kA",                                   // 电流单位
        "V", "mV", "kV", "MV",                                   // 电压单位
        "W", "kW", "MW", "mW", "μW",                             // 功率单位
        "J", "kJ", "MJ", "cal", "kcal",                          // 能量单位
        "Hz", "kHz", "MHz", "GHz",                               // 频率单位
        "Pa", "kPa", "MPa", "bar", "atm",                        // 压强单位
        "°C", "°F", "K",                                         // 温度单位
        "mol", "mmol", "μmol", "kmol",                           // 物质的量单位
        "cd", "lm", "lx",                                        // 光学单位
        "Ω", "kΩ", "MΩ", "mΩ", "μΩ",                            // 电阻单位
        "F", "μF", "nF", "pF",                                   // 电容单位
        "H", "mH", "μH", "nH",                                   // 电感单位
        "T", "mT", "μT", "nT", "Wb",                             // 磁学单位
        "N", "kN", "MN", "mN", "μN",                             // 力单位
        "m²", "cm²", "mm²", "km²", "ha", "acre",                 // 面积单位
        "m³", "cm³", "mm³", "L", "mL", "μL",                     // 体积单位
        "m/s", "km/h", "mph",                                    // 速度单位
        "m/s²", "g₀"                                             // 加速度单位
    };

    // 常见英文单词（避免误转换）
    private static readonly HashSet<string> CommonWords = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
    {
        "a", "an", "and", "or", "the", "is", "are", "was", "were", "be", "been", "being",
        "has", "have", "had", "do", "does", "did", "will", "would", "could", "should",
        "can", "may", "might", "must", "shall", "to", "of", "for", "with", "by", "at",
        "in", "on", "as", "if", "so", "no", "not", "but", "up", "out", "all", "any",
        "own", "one", "two", "new", "old", "get", "see", "way", "use", "man", "day",
        "time", "year", "work", "life", "hand", "part", "eye", "know", "back", "give",
        "most", "good", "woman", "fact", "work", "week", "case", "point", "group",
        "problem", "part", "place", "seem", "ask", "feel", "try", "leave", "call"
    };

    static ConvertTextToMathStep()
    {
        // 初始化允许的字符集合
        // 小写字母
        for (char c = 'a'; c <= 'z'; c++)
            AllowedChars.Add(c);
        
        // 数字
        for (char c = '0'; c <= '9'; c++)
            AllowedChars.Add(c);
        
        // 数学运算符
        foreach (char c in MathOperators)
            AllowedChars.Add(c);
        
        // 中英文括号
        AllowedChars.Add('(');
        AllowedChars.Add(')');
        AllowedChars.Add('[');
        AllowedChars.Add(']');
        AllowedChars.Add('{');
        AllowedChars.Add('}');
        AllowedChars.Add('（');
        AllowedChars.Add('）');
        AllowedChars.Add('【');
        AllowedChars.Add('】');
        
        // 上下标字符
        AllowedChars.Add('²');
        AllowedChars.Add('³');
        AllowedChars.Add('¹');
        AllowedChars.Add('⁰');
        AllowedChars.Add('⁴');
        AllowedChars.Add('⁵');
        AllowedChars.Add('⁶');
        AllowedChars.Add('⁷');
        AllowedChars.Add('⁸');
        AllowedChars.Add('⁹');
        AllowedChars.Add('₀');
        AllowedChars.Add('₁');
        AllowedChars.Add('₂');
        AllowedChars.Add('₃');
        AllowedChars.Add('₄');
        AllowedChars.Add('₅');
        AllowedChars.Add('₆');
        AllowedChars.Add('₇');
        AllowedChars.Add('₈');
        AllowedChars.Add('₉');
    }

    /// <summary>
    /// 执行文本转公式处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取所有段落（不包括页眉页脚，但包括表格）
            var paragraphs = GetProcessableParagraphs(doc);
            
            foreach (var paragraph in paragraphs)
            {
                ProcessParagraph(paragraph);
            }
            
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 获取可处理的段落列表
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetProcessableParagraphs(Document doc)
    {
        var paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        var allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 跳过页眉页脚
            if (IsInHeaderFooter(para))
                continue;
            
            paragraphs.Add(para);
        }
        
        return paragraphs;
    }

    /// <summary>
    /// 检查节点是否在页眉或页脚中
    /// </summary>
    /// <param name="node">要检查的节点</param>
    /// <returns>如果在页眉或页脚中返回true，否则返回false</returns>
    private static bool IsInHeaderFooter(Node node)
    {
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 处理单个段落
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    private void ProcessParagraph(Paragraph paragraph)
    {
        // 获取段落的完整文本用于分析
        string fullText = paragraph.ToString(SaveFormat.Text).Trim();
        
        // 跳过空段落
        if (string.IsNullOrWhiteSpace(fullText))
            return;
        
        // 获取段落中的所有Run节点
        var runs = paragraph.GetChildNodes(NodeType.Run, false).Cast<Run>().ToList();
        
        if (runs.Count == 0)
            return;

        // 在完整文本中查找数学表达式
        var mathExpressions = FindMathExpressionsInText(fullText);
        
        // 如果找到数学表达式，执行转换
        if (mathExpressions.Count > 0)
        {
            // 添加调试信息
            Console.WriteLine($"段落文本: {fullText}");
            Console.WriteLine($"找到 {mathExpressions.Count} 个数学表达式:");
            foreach (var expr in mathExpressions)
            {
                Console.WriteLine($"  - '{expr.Text}' (位置 {expr.StartIndex}-{expr.EndIndex})");
            }
            
            ConvertToMathInParagraph(mathExpressions, paragraph, fullText);
        }
    }

    /// <summary>
    /// 数学表达式信息（简化版）
    /// </summary>
    private class SimpleMathExpression
    {
        public string Text { get; set; } = string.Empty;
        public int StartIndex { get; set; }
        public int EndIndex { get; set; }
    }

    /// <summary>
    /// 在完整文本中查找数学表达式
    /// </summary>
    /// <param name="text">完整文本</param>
    /// <returns>数学表达式列表</returns>
    private List<SimpleMathExpression> FindMathExpressionsInText(string text)
    {
        var expressions = new List<SimpleMathExpression>();
        
        // 使用正则表达式查找潜在的数学表达式
        // 匹配模式：包含字母、数字、运算符、括号的连续文本
        var mathPattern = @"[a-z0-9+\-*/=<>≤≥≠≈±∓×÷∑∏∫√∞∂∆∇∈∉⊂⊃∩∪∅∀∃→←↔⇒⇐⇔()[\]{}（）【】²³¹⁰⁴⁵⁶⁷⁸⁹₀₁₂₃₄₅₆₇₈₉\s]+";
        
        var matches = Regex.Matches(text, mathPattern, RegexOptions.IgnoreCase);
        
        foreach (Match match in matches)
        {
            string candidate = match.Value.Trim();
            
            // 对每个候选文本进行详细判断
            if (IsMathExpression(candidate))
            {
                expressions.Add(new SimpleMathExpression
                {
                    Text = candidate,
                    StartIndex = match.Index,
                    EndIndex = match.Index + match.Length
                });
            }
        }
        
        return expressions;
    }

    /// <summary>
    /// 判断是否为数学表达式
    /// </summary>
    /// <param name="text">文本</param>
    /// <returns>是否为数学表达式</returns>
    private bool IsMathExpression(string text)
    {
        if (string.IsNullOrWhiteSpace(text))
            return false;
        
        // 排除常见单词
        if (CommonWords.Contains(text))
            return false;
        
        // 排除单位
        if (UnitWords.Contains(text))
            return false;
        
        // 检查是否只包含允许的字符
        if (!text.All(c => AllowedChars.Contains(c)))
            return false;
        
        // 单独一个小写字母
        if (text.Length == 1 && char.IsLower(text[0]))
            return true;
        
        // 单独一个小写字母加上下标
        if (IsSingleVariableWithScript(text))
            return true;
        
        // 检查是否至少包含一个小写字母和一个数学运算符
        bool hasLowerLetter = text.Any(c => char.IsLower(c));
        bool hasMathOperator = text.Any(c => MathOperators.Contains(c));
        
        if (hasLowerLetter && hasMathOperator)
            return true;
        
        return false;
    }

    /// <summary>
    /// 检查是否为单个变量加上下标
    /// </summary>
    /// <param name="text">文本</param>
    /// <returns>是否为单个变量加上下标</returns>
    private bool IsSingleVariableWithScript(string text)
    {
        if (text.Length < 2)
            return false;
        
        // 第一个字符必须是小写字母
        if (!char.IsLower(text[0]))
            return false;
        
        // 其余字符必须是数字或上下标字符
        for (int i = 1; i < text.Length; i++)
        {
            char c = text[i];
            if (!char.IsDigit(c) && !IsSubscriptOrSuperscript(c))
                return false;
        }
        
        return true;
    }

    /// <summary>
    /// 检查字符是否为上下标字符
    /// </summary>
    /// <param name="c">字符</param>
    /// <returns>是否为上下标字符</returns>
    private bool IsSubscriptOrSuperscript(char c)
    {
        return (c >= '₀' && c <= '₉') || // 下标数字
               (c >= '⁰' && c <= '⁹') || // 上标数字
               c == '²' || c == '³' || c == '¹'; // 常见上标
    }

    /// <summary>
    /// 在段落中转换数学表达式
    /// </summary>
    /// <param name="expressions">数学表达式列表</param>
    /// <param name="paragraph">段落对象</param>
    /// <param name="fullText">段落完整文本</param>
    private void ConvertToMathInParagraph(List<SimpleMathExpression> expressions, Paragraph paragraph, string fullText)
    {
        try
        {
            // 从后往前处理，避免索引变化
            for (int i = expressions.Count - 1; i >= 0; i--)
            {
                var expression = expressions[i];
                
                // 查找包含此表达式的Run节点并进行替换
                ReplaceTextWithMath(paragraph, expression.Text);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"转换数学表达式时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 在段落中查找并替换指定文本为数学公式
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <param name="mathText">要替换的数学文本</param>
    private void ReplaceTextWithMath(Paragraph paragraph, string mathText)
    {
        try
        {
            var doc = (Document)paragraph.Document;
            var builder = new DocumentBuilder(doc);
            
            // 定位到段落
            builder.MoveTo(paragraph);
            
            // 在段落中查找文本并替换为公式
            var options = new FindReplaceOptions();
            options.ReplacingCallback = new MathReplacingCallback(mathText);
            
            // 使用文档级别的替换，指定段落范围
            paragraph.Range.Replace(mathText, "", options);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"替换文本时出错: {ex.Message}");
        }
    }

    /// <summary>
    /// 数学公式替换回调
    /// </summary>
    private class MathReplacingCallback : IReplacingCallback
    {
        private readonly string _mathText;

        public MathReplacingCallback(string mathText)
        {
            _mathText = mathText;
        }

        public ReplaceAction Replacing(ReplacingArgs args)
        {
            try
            {
                var builder = new DocumentBuilder((Document)args.MatchNode.Document);
                builder.MoveTo(args.MatchNode);
                
                // 删除匹配的文本
                args.MatchNode.Remove();
                
                // 插入EQ字段公式（这是创建数学公式的标准方式）
                string mathFieldCode = $"EQ \\i({ProcessMathText(_mathText)})";
                builder.InsertField(mathFieldCode, "");
                
                Console.WriteLine($"成功转换: '{_mathText}' -> EQ字段");
                
                return ReplaceAction.Skip;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"替换回调出错: {ex.Message}");
                return ReplaceAction.Skip;
            }
        }
        
        /// <summary>
        /// 处理数学文本
        /// </summary>
        /// <param name="text">原始文本</param>
        /// <returns>处理后的数学文本</returns>
        private string ProcessMathText(string text)
        {
            // 简单清理，保持原文本
            return text.Trim();
        }
    }
} 