using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Math;
using Aspose.Words.Replacing;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// =========================== 标准化序号步骤类 ===========================
// 实现IPipelineStep接口，专门处理文档中的英文括号序号标准化
// 功能：将"(1)"、"(2)"、"(a)"等替换为"（1）"、"（2）"、"（a）"等
public sealed class NormalizeSequenceStep : IPipelineStep
{
    /// <summary>
    /// 需要标准化的序号字段列表
    /// </summary>
    private readonly List<string> _targetSequences;

    /// <summary>
    /// 构造函数，初始化目标序号字段列表
    /// </summary>
    /// <param name="targetSequences">需要处理的序号字段列表（如"1", "2", "a", "b"等）</param>
    public NormalizeSequenceStep(List<string> targetSequences)
    {
        _targetSequences = targetSequences ?? throw new ArgumentNullException(nameof(targetSequences));
    }

    /// <summary>
    /// 执行序号标准化处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 如果没有指定字段，直接返回成功
            if (_targetSequences.Count == 0)
                return true;

            // ============================================ 正则初始化 ============================================
            // 构建正则表达式模式，匹配英文括号包围的指定字段
            string pattern = BuildRegexPattern();
            var regex = new Regex(pattern, RegexOptions.Compiled);

            // ==================== 替换配置 ====================
            var options = new FindReplaceOptions
            {
                // 绑定自定义回调处理器（规避公式区域和页眉页脚）
                ReplacingCallback = new FormulaSafeReplacer(),
                // 启用替换组功能，支持$1捕获组语法
                UseSubstitutions = true
            };

            // ===================== 执行全局替换 =====================
            // 使用正则表达式将"(字段)"替换为"（字段）"
            // $1表示第一个捕获组，即括号中间的字段内容
            doc.Range.Replace(regex, "（$1）", options);
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 构建正则表达式模式
    /// </summary>
    /// <returns>用于匹配目标序号的正则表达式字符串</returns>
    private string BuildRegexPattern()
    {
        // 对每个字段进行正则转义，防止特殊字符影响匹配
        var escapedSequences = _targetSequences.Select(Regex.Escape);
        
        // 构建选择模式：(field1|field2|field3)
        string sequencePattern = string.Join("|", escapedSequences);
        
        // 完整模式：匹配英文括号包围的指定字段，但排除前面紧跟字母的情况
        // (?<![a-zA-Z]) : 负向后顾断言，确保"("前面不是英文字母
        // \( : 匹配字面量左括号
        // (...) : 捕获组，用于替换时保留字段内容
        // \) : 匹配字面量右括号
        return $@"(?<![a-zA-Z])\(({sequencePattern})\)";
    }

    /// <summary>
    /// 公式和页眉页脚安全替换器
    /// </summary>
    private class FormulaSafeReplacer : IReplacingCallback
    {
        /// <summary>
        /// 替换回调核心方法，控制是否执行替换
        /// </summary>
        /// <param name="args">替换事件参数</param>
        /// <returns>替换操作指令</returns>
        public ReplaceAction Replacing(ReplacingArgs args)
        {
            // ===================== 公式节点检测 =====================
            // 跳过微软公式节点，避免破坏公式结构
            if (IsInOfficeMath(args.MatchNode))
                return ReplaceAction.Skip;

            // ===================== 页眉页脚检测 =====================
            // 跳过页眉页脚区域的内容
            if (IsInHeaderFooter(args.MatchNode))
                return ReplaceAction.Skip;

            // ===================== 执行替换 =====================
            // 在正文区域且非公式内容时执行替换
            return ReplaceAction.Replace;
        }

        /// <summary>
        /// 检查节点是否位于微软公式内
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>如果在公式内返回true，否则返回false</returns>
        private bool IsInOfficeMath(Node node)
        {
            // 向上遍历节点树，查找OfficeMath祖先节点
            while (node != null)
            {
                if (node is OfficeMath)
                    return true;
                node = node.ParentNode;
            }
            return false;
        }

        /// <summary>
        /// 检查节点是否位于页眉或页脚内
        /// </summary>
        /// <param name="node">要检查的节点</param>
        /// <returns>如果在页眉页脚内返回true，否则返回false</returns>
        private bool IsInHeaderFooter(Node node)
        {
            // 使用GetAncestor方法快速查找HeaderFooter祖先节点
            return node.GetAncestor(typeof(HeaderFooter)) != null;
        }
    }
} 