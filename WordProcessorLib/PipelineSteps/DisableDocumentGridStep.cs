using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// 文档网格处理步骤（彻底无网格方案）
// 实现原理：模拟Word的「无网格」设置组合
public class DisableDocumentGridStep : IPipelineStep
{
    // 主处理方法 - 同时修正页面和段落设置
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // ================== 第一阶段：页面级设置 ==================
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                PageSetup page = section.PageSetup;

                /* 
                 * 关键设置组合（模拟Word的无网格设置）：
                 * 1. 使用默认布局模式（非网格模式）
                 * 2. 禁用字符网格限制
                 * 3. 禁用行网格限制
                 */
                page.LayoutMode = SectionLayoutMode.Default;    // 必须设为默认模式
                page.CharactersPerLine = 1;                     // 设为最小值避免限制
                page.LinesPerPage = 1;                          // 设为最小值避免限制
            }

            // ================== 第二阶段：段落级设置 ==================
            var paragraphs = doc.GetChildNodes(NodeType.Paragraph, true)
                               .OfType<Paragraph>();
            
            foreach (var para in paragraphs.Where(p => !IsEmptyParagraph(p)))
            {
                ParagraphFormat fmt = para.ParagraphFormat;
                
                // 核心设置组合
                fmt.SnapToGrid = false;                             // 禁用段落级对齐
                fmt.LineSpacingRule = LineSpacingRule.Exactly;      // 固定行距
                fmt.LineSpacing = 12;                               // 12磅=单倍行距
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 检测空段落（优化性能）
    private bool IsEmptyParagraph(Paragraph para)
    {
        return !para.Runs.OfType<Run>()
                     .Any(r => !string.IsNullOrWhiteSpace(r.GetText()?.Trim()));
    }
}