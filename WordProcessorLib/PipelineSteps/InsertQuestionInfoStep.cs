using System.Text.RegularExpressions;
using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 插入试题信息步骤（实现IPipelineStep接口）
/// 在每道题题号后面插入指定的试题信息标签
/// 如果插入文本为null或空字符串，则跳过该步骤
/// </summary>
public sealed class InsertQuestionInfoStep : IPipelineStep
{
    private readonly string _insertText;
    private readonly int _insertPosition;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="insertText">要插入的文本（如"［2025·广东卷］"），为null时跳过该步骤</param>
    /// <param name="insertPosition">插入位置（1表示紧跟题号，2表示第一个［］后，依此类推）</param>
    public InsertQuestionInfoStep(string insertText, int insertPosition)
    {
        _insertText = insertText; // 允许为null，表示跳过该步骤
        _insertPosition = Math.Max(1, insertPosition); // 确保位置参数至少为1
    }

    /// <summary>
    /// 执行插入试题信息处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 如果插入文本为空，则跳过该步骤
            if (string.IsNullOrEmpty(_insertText))
            {
                return true;
            }

            // 获取文档中不在表格内的所有段落
            var paragraphs = GetNonTableParagraphs(doc);
            
            if (paragraphs.Count == 0)
            {
                return true;
            }

            // 查找所有题号段落并插入试题信息
            ProcessQuestionParagraphs(paragraphs);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文档中不在表格内的所有段落
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonTableParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 检查父节点是否为表格单元格
            bool isInTable = false;
            Node parent = para.ParentNode;
            while (parent != null)
            {
                if (parent is Aspose.Words.Tables.Cell)
                {
                    isInTable = true;
                    break;
                }
                parent = parent.ParentNode;
            }
            
            // 如果不在表格中，则添加到列表
            if (!isInTable)
            {
                paragraphs.Add(para);
            }
        }
        
        return paragraphs;
    }

    /// <summary>
    /// 处理文档中的所有题号段落
    /// </summary>
    /// <param name="paragraphs">文档段落列表</param>
    private void ProcessQuestionParagraphs(List<Paragraph> paragraphs)
    {
        // 题号匹配正则表达式（严格匹配数字+中文点）
        Regex questionNumberPattern = new Regex(@"^\s*(\d+)．", RegexOptions.Compiled);
        
        foreach (Paragraph paragraph in paragraphs)
        {
            string text = GetParagraphText(paragraph);
            
            // 跳过空段落
            if (string.IsNullOrWhiteSpace(text))
            {
                continue;
            }
            
            // 检查是否为题号段落
            Match questionMatch = questionNumberPattern.Match(text);
            if (questionMatch.Success)
            {
                string questionNumber = questionMatch.Groups[1].Value;
                ProcessQuestionParagraph(paragraph, questionNumber);
            }
        }
    }

    /// <summary>
    /// 获取段落的纯文本内容
    /// </summary>
    private string GetParagraphText(Paragraph paragraph)
    {
        return paragraph.ToString(SaveFormat.Text).Trim();
    }

    /// <summary>
    /// 处理单个题号段落
    /// </summary>
    /// <param name="paragraph">题号段落</param>
    /// <param name="questionNumber">题号</param>
    private void ProcessQuestionParagraph(Paragraph paragraph, string questionNumber)
    {
        // 分析段落中现有的［］标签
        var bracketInfo = AnalyzeBracketsInParagraph(paragraph, questionNumber);
        
        // 确定插入位置
        int actualInsertPosition = Math.Min(_insertPosition, bracketInfo.BracketCount + 1);
        
        // 执行插入操作
        InsertTextAtPosition(paragraph, bracketInfo, actualInsertPosition);
    }

    /// <summary>
    /// 分析段落中的［］标签信息
    /// </summary>
    private BracketAnalysisResult AnalyzeBracketsInParagraph(Paragraph paragraph, string questionNumber)
    {
        var result = new BracketAnalysisResult();
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        
        // 首先找到题号的结束位置
        string questionPattern = questionNumber + "．";
        int questionEndPosition = FindQuestionEndPosition(runs, questionPattern);
        
        if (questionEndPosition == -1)
        {
            return result; // 未找到题号
        }
        
        // 从题号后开始分析［］标签
        AnalyzeBracketsFromPosition(runs, questionEndPosition, result);
        
        return result;
    }

    /// <summary>
    /// 查找题号结束位置
    /// </summary>
    private int FindQuestionEndPosition(NodeCollection runs, string questionPattern)
    {
        string fullText = "";
        List<(int start, int end, Run run, int runIndex)> runMap = new List<(int, int, Run, int)>();
        
        for (int i = 0; i < runs.Count; i++)
        {
            Run run = (Run)runs[i];
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run, i));
        }
        
        int patternIndex = fullText.IndexOf(questionPattern);
        if (patternIndex == -1) return -1;
        
        return patternIndex + questionPattern.Length;
    }

    /// <summary>
    /// 从指定位置开始分析［］标签
    /// </summary>
    private void AnalyzeBracketsFromPosition(NodeCollection runs, int startPosition, BracketAnalysisResult result)
    {
        string fullText = "";
        List<(int start, int end, Run run, int runIndex)> runMap = new List<(int, int, Run, int)>();
        
        for (int i = 0; i < runs.Count; i++)
        {
            Run run = (Run)runs[i];
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run, i));
        }
        
        // 从题号后开始查找［］对
        string remainingText = fullText.Substring(Math.Min(startPosition, fullText.Length));
        Regex bracketPairPattern = new Regex(@"［[^［］]*］", RegexOptions.Compiled);
        MatchCollection matches = bracketPairPattern.Matches(remainingText);
        
        // 检查是否连续出现
        int currentPos = 0;
        foreach (Match match in matches)
        {
            // 检查前面是否有非空白字符（除了上一个］）
            string beforeMatch = remainingText.Substring(currentPos, match.Index - currentPos).Trim();
            if (!string.IsNullOrEmpty(beforeMatch) && result.BracketCount > 0)
            {
                break; // 不连续，停止计数
            }
            
            result.BracketCount++;
            result.LastBracketEndPosition = startPosition + match.Index + match.Length;
            currentPos = match.Index + match.Length;
        }
        
        // 找到插入位置对应的Run信息
        FindInsertionRunInfo(runMap, result);
    }

    /// <summary>
    /// 查找插入位置的Run信息
    /// </summary>
    private void FindInsertionRunInfo(List<(int start, int end, Run run, int runIndex)> runMap, BracketAnalysisResult result)
    {
        int targetPosition = result.LastBracketEndPosition;
        
        foreach (var (start, end, run, runIndex) in runMap)
        {
            if (targetPosition >= start && targetPosition <= end)
            {
                result.TargetRun = run;
                result.TargetRunIndex = runIndex;
                result.TargetRunOffset = targetPosition - start;
                break;
            }
        }
        
        // 如果没找到合适的Run，使用最后一个Run
        if (result.TargetRun == null && runMap.Count > 0)
        {
            var lastRun = runMap[runMap.Count - 1];
            result.TargetRun = lastRun.run;
            result.TargetRunIndex = lastRun.runIndex;
            result.TargetRunOffset = lastRun.run.Text.Length;
        }
    }

    /// <summary>
    /// 在指定位置插入文本
    /// </summary>
    private void InsertTextAtPosition(Paragraph paragraph, BracketAnalysisResult bracketInfo, int insertPosition)
    {
        if (insertPosition == 1)
        {
            // 插入到题号后
            InsertAfterQuestionNumber(paragraph);
        }
        else
        {
            // 插入到指定［］后
            InsertAfterBrackets(paragraph, bracketInfo);
        }
    }

    /// <summary>
    /// 在题号后插入文本
    /// </summary>
    private void InsertAfterQuestionNumber(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        
        // 查找包含题号的Run
        foreach (Run run in runs)
        {
            Match match = Regex.Match(run.Text, @"(\d+)．");
            if (match.Success)
            {
                InsertTextInRun(run, match.Index + match.Length, run);
                return;
            }
        }
        
        // 如果单个Run中找不到，尝试合并Run查找
        InsertAfterQuestionNumberInMergedRuns(runs);
    }

    /// <summary>
    /// 在合并的Run中查找题号并插入
    /// </summary>
    private void InsertAfterQuestionNumberInMergedRuns(NodeCollection runs)
    {
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        Match match = Regex.Match(fullText, @"(\d+)．");
        if (match.Success)
        {
            int insertPosition = match.Index + match.Length;
            
            // 找到对应的Run
            foreach (var (start, end, run) in runMap)
            {
                if (insertPosition >= start && insertPosition <= end)
                {
                    InsertTextInRun(run, insertPosition - start, run);
                    return;
                }
            }
        }
    }

    /// <summary>
    /// 在［］后插入文本
    /// </summary>
    private void InsertAfterBrackets(Paragraph paragraph, BracketAnalysisResult bracketInfo)
    {
        if (bracketInfo.TargetRun != null)
        {
            InsertTextInRun(bracketInfo.TargetRun, bracketInfo.TargetRunOffset, bracketInfo.TargetRun);
        }
    }

    /// <summary>
    /// 在Run中的指定位置插入文本
    /// </summary>
    private void InsertTextInRun(Run targetRun, int insertOffset, Run styleSourceRun)
    {
        // 分割Run
        string beforeText = targetRun.Text.Substring(0, insertOffset);
        string afterText = targetRun.Text.Substring(insertOffset);
        
        // 修改当前Run为前半部分
        targetRun.Text = beforeText;
        
        // 创建插入文本的Run，克隆原Run的字体样式
        Run insertRun = new Run(targetRun.Document);
        insertRun.Text = _insertText;
        CloneFontProperties(styleSourceRun, insertRun);
        
        // 创建后半部分Run
        Run afterRun = new Run(targetRun.Document);
        afterRun.Text = afterText;
        CloneFontProperties(styleSourceRun, afterRun);
        
        // 插入新的Run节点
        targetRun.ParentNode.InsertAfter(insertRun, targetRun);
        if (!string.IsNullOrEmpty(afterText))
        {
            targetRun.ParentNode.InsertAfter(afterRun, insertRun);
        }
    }

    /// <summary>
    /// 克隆字体属性
    /// </summary>
    private void CloneFontProperties(Run sourceRun, Run targetRun)
    {
        targetRun.Font.Name = sourceRun.Font.Name;
        targetRun.Font.NameFarEast = sourceRun.Font.NameFarEast;
        targetRun.Font.Size = sourceRun.Font.Size;
        targetRun.Font.Bold = sourceRun.Font.Bold;
        targetRun.Font.Italic = sourceRun.Font.Italic;
        targetRun.Font.Color = sourceRun.Font.Color;
        targetRun.Font.Underline = sourceRun.Font.Underline;
    }

    /// <summary>
    /// ［］标签分析结果
    /// </summary>
    private class BracketAnalysisResult
    {
        public int BracketCount { get; set; } = 0;
        public int LastBracketEndPosition { get; set; } = 0;
        public Run TargetRun { get; set; } = null;
        public int TargetRunIndex { get; set; } = -1;
        public int TargetRunOffset { get; set; } = 0;
    }
} 