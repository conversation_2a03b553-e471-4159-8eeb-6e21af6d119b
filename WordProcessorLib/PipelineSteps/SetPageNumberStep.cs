using Aspose.Words;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

// 页码处理步骤（IPipelineStep实现）
public class SetPageNumberStep : IPipelineStep
{
    // 主执行方法：入口点
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            foreach (Section section in doc.Sections)
            {
                ClearExistingFooters(section);
                CreateUniversalFooter(section, doc);
            }

            // 性能修复：移除doc.UpdateFields()，让Word自动刷新
            // doc.UpdateFields();
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
    
    // 清除指定节的所有页脚内容（最小化修改：直接清除，无需判断内容）
    private void ClearExistingFooters(Section section)
    {
        HeaderFooterType[] footerTypes = 
        {
            HeaderFooterType.FooterPrimary,  // 标准页脚
            HeaderFooterType.FooterFirst,    // 首页页脚
            HeaderFooterType.FooterEven      // 偶数页页脚
        };

        foreach (var footerType in footerTypes)
        {
            HeaderFooter footer = section.HeadersFooters[footerType];
            if (footer == null) continue;
            // 直接清除内容，无需判断
            footer.RemoveAllChildren();
        }
    }

    // 创建统一页码页脚（原逻辑不变）
    private void CreateUniversalFooter(Section section, Document mainDoc)
    {
        HeaderFooterType[] footerTypes = 
        {
            HeaderFooterType.FooterPrimary,
            HeaderFooterType.FooterEven
        };

        foreach (var footerType in footerTypes)
        {
            HeaderFooter footer = section.HeadersFooters[footerType] 
                                ?? new HeaderFooter(mainDoc, footerType);
            footer.RemoveAllChildren();

            DocumentBuilder builder = new DocumentBuilder(mainDoc);
            try
            {
                builder.MoveToHeaderFooter(footerType);
                builder.ParagraphFormat.Alignment = ParagraphAlignment.Center;
                builder.ParagraphFormat.LeftIndent = 0;
                builder.ParagraphFormat.SpaceAfter = 0;
                builder.Font.Name = "XITS Math";
                builder.Font.Size = FontSizeConverter.GetPointSize("小五");
                builder.Font.Bold = false;
                builder.InsertField("PAGE", "");
            }
            finally
            {
                builder.MoveToDocumentEnd();
            }
            if (section.HeadersFooters[footerType] == null)
            {
                section.HeadersFooters.Add(footer);
            }
        }
    }
}