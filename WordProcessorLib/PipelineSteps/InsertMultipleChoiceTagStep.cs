using System.Text.RegularExpressions;
using Aspose.Words;
using WordProcessorLib.Interfaces;
using System.Linq;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 插入多选题标签步骤（实现IPipelineStep接口）
/// 在多选题的题号后面插入"［多选题］"标签
/// </summary>
public sealed class InsertMultipleChoiceTagStep : IPipelineStep
{
    /// <summary>
    /// 执行插入多选题标签处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中不在表格内的所有段落，参考InsertEndTagStep的做法
            var paragraphs = GetNonTableParagraphs(doc);
            
            if (paragraphs.Count == 0)
            {
                return true;
            }

            // 查找所有符合【答案】AB格式的段落并处理对应题目
            ProcessMultipleChoiceSections(paragraphs);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文档中不在表格内的所有段落（参考InsertEndTagStep）
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonTableParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 检查父节点是否为表格单元格
            bool isInTable = false;
            Node parent = para.ParentNode;
            while (parent != null)
            {
                if (parent is Aspose.Words.Tables.Cell)
                {
                    isInTable = true;
                    break;
                }
                parent = parent.ParentNode;
            }
            
            // 如果不在表格中，则添加到列表
            if (!isInTable)
            {
                paragraphs.Add(para);
            }
        }
        
        return paragraphs;
    }

    /// <summary>
    /// 处理文档中的所有多选题部分
    /// </summary>
    /// <param name="paragraphs">文档段落列表</param>
    private void ProcessMultipleChoiceSections(List<Paragraph> paragraphs)
    {
        // 记录已处理的段落索引，避免重复处理
        HashSet<int> processedParagraphs = new HashSet<int>();
        
        // 遍历所有段落，查找符合【答案】AB格式的段落
        for (int i = 0; i < paragraphs.Count; i++)
        {
            // 跳过已处理的段落
            if (processedParagraphs.Contains(i))
            {
                continue;
            }
            
            // 检查当前段落是否为多选题答案段落
            if (IsMultipleChoiceAnswerParagraph(paragraphs[i]))
            {
                // 从当前位置往前寻找题号并插入标签
                int questionIndex = FindQuestionNumber(paragraphs, i);
                if (questionIndex >= 0 && !processedParagraphs.Contains(questionIndex))
                {
                    InsertMultipleChoiceTagInParagraph(paragraphs[questionIndex]);
                    processedParagraphs.Add(questionIndex);
                }
            }
        }
    }

    /// <summary>
    /// 判断段落是否为多选题答案段落
    /// 检查段落是否只包含【答案】开头且后跟2个或以上大写字母ABCDEFG的格式
    /// </summary>
    /// <param name="paragraph">要检查的段落</param>
    /// <returns>是否为多选题答案段落</returns>
    private bool IsMultipleChoiceAnswerParagraph(Paragraph paragraph)
    {
        try
        {
            // 检查段落是否包含图片或其他非文本内容
            NodeCollection shapes = paragraph.GetChildNodes(NodeType.Shape, true);
            if (shapes.Count > 0)
            {
                return false;
            }

            // 获取段落的所有文本内容（跨Run合并）
            string fullText = GetParagraphFullText(paragraph);
            
            // 去除所有空白字符进行检查
            string trimmedText = fullText.Replace(" ", "").Replace("\t", "").Replace("\r", "").Replace("\n", "");
            
            // 检查是否符合【答案】+2个或以上大写字母的格式
            return Regex.IsMatch(trimmedText, @"^【答案】[ABCDEFG]{2,}$");
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取段落的完整文本内容（处理跨Run情况）
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <returns>段落的完整文本</returns>
    private string GetParagraphFullText(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        
        foreach (Run run in runs)
        {
            fullText += run.Text;
        }
        
        return fullText;
    }

    /// <summary>
    /// 从指定位置往前寻找第一个题号段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">开始搜索的索引</param>
    /// <returns>题号段落的索引，如果未找到返回-1</returns>
    private int FindQuestionNumber(List<Paragraph> paragraphs, int startIndex)
    {
        // 题号匹配正则表达式
        Regex questionStartPattern = new Regex(@"^\s*\d+[\.．。]\s*", RegexOptions.Compiled);
        
        // 从当前位置往前搜索
        for (int i = startIndex - 1; i >= 0; i--)
        {
            string text = paragraphs[i].ToString(SaveFormat.Text).Trim();
            
            // 跳过空段落
            if (string.IsNullOrWhiteSpace(text))
            {
                continue;
            }
            
            // 检查是否是题号开头
            if (questionStartPattern.IsMatch(text))
            {
                return i;
            }
        }
        
        return -1;
    }

    /// <summary>
    /// 在段落中插入多选题标签（处理Run分割问题）
    /// </summary>
    private void InsertMultipleChoiceTagInParagraph(Paragraph paragraph)
    {
        // 获取段落的完整文本，用于提取题号
        string fullText = paragraph.ToString(SaveFormat.Text).Trim();
        
        // 提取题号
        Match match = Regex.Match(fullText, @"^(\d+)[\.．。]");
        if (!match.Success)
        {
            return;
        }
        
        string questionNumber = match.Groups[1].Value;
        
        // 检查题号后是否已存在多选题标签
        var tagCheckResult = CheckExistingMultipleChoiceTags(paragraph, questionNumber);
        
        if (tagCheckResult.HasMultipleChoiceTag)
        {
            // 如果存在重复标签，清理多余的
            if (tagCheckResult.DuplicateCount > 1)
            {
                CleanupDuplicateMultipleChoiceTags(paragraph, questionNumber, tagCheckResult.DuplicateCount);
            }
            // 已存在标签，跳过插入
            return;
        }
        
        // 不存在多选题标签，正常插入
        string targetPattern = questionNumber + "．"; // 优先处理中文句号
        ProcessRunsForInsertion(paragraph, questionNumber, targetPattern);
    }

    /// <summary>
    /// 标签检查结果
    /// </summary>
    private class TagCheckResult
    {
        public bool HasMultipleChoiceTag { get; set; }
        public int DuplicateCount { get; set; }
    }

    /// <summary>
    /// 检查题号后是否已存在多选题标签
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <param name="questionNumber">题号</param>
    /// <returns>标签检查结果</returns>
    private TagCheckResult CheckExistingMultipleChoiceTags(Paragraph paragraph, string questionNumber)
    {
        var result = new TagCheckResult();
        
        try
        {
            // 获取段落完整文本
            string fullText = GetParagraphFullText(paragraph);
            
            // 查找题号位置
            int questionEndPos = FindQuestionEndPosition(fullText, questionNumber);
            if (questionEndPos == -1)
            {
                return result;
            }
            
            // 解析题号后的所有标签
            var tags = ParseTagsAfterQuestion(fullText, questionEndPos);
            int multipleChoiceTagCount = tags.Count(tag => tag.content.Trim() == "多选题");
            
            result.HasMultipleChoiceTag = multipleChoiceTagCount > 0;
            result.DuplicateCount = multipleChoiceTagCount;
        }
        catch
        {
            // 出现异常时，默认返回无标签
        }
        
        return result;
    }

    /// <summary>
    /// 标签信息
    /// </summary>
    private class TagInfo
    {
        public int Start { get; set; }
        public int End { get; set; }
        public string Content { get; set; } = "";
        public string content => Content; // 小写属性用于兼容LINQ
    }

    /// <summary>
    /// 查找题号结束位置
    /// </summary>
    /// <param name="text">完整文本</param>
    /// <param name="questionNumber">题号</param>
    /// <returns>题号结束位置，找不到返回-1</returns>
    private int FindQuestionEndPosition(string text, string questionNumber)
    {
        string[] patterns = { questionNumber + "．", questionNumber + ".", questionNumber + "。" };
        
        foreach (string pattern in patterns)
        {
            int pos = text.IndexOf(pattern);
            if (pos >= 0)
            {
                return pos + pattern.Length;
            }
        }
        
        return -1;
    }

    /// <summary>
    /// 解析题号后的所有标签
    /// </summary>
    /// <param name="text">完整文本</param>
    /// <param name="startPos">开始位置</param>
    /// <returns>标签列表</returns>
    private List<TagInfo> ParseTagsAfterQuestion(string text, int startPos)
    {
        var tags = new List<TagInfo>();
        int currentPos = startPos;
        
        while (currentPos < text.Length)
        {
            // 跳过空白字符
            while (currentPos < text.Length && char.IsWhiteSpace(text[currentPos]))
            {
                currentPos++;
            }
            
            if (currentPos >= text.Length)
            {
                break;
            }
            
            // 检查是否是标签开始
            if (text[currentPos] == '[' || text[currentPos] == '［')
            {
                int tagStart = currentPos;
                int contentStart = currentPos + 1;
                int tagEnd = FindTagEndInText(text, contentStart);
                
                if (tagEnd > contentStart)
                {
                    string tagContent = text.Substring(contentStart, tagEnd - contentStart);
                    tags.Add(new TagInfo 
                    { 
                        Start = tagStart, 
                        End = tagEnd + 1, // 包含右括号
                        Content = tagContent 
                    });
                    currentPos = tagEnd + 1;
                }
                else
                {
                    // 没有找到标签结束，查找【答案】位置作为边界
                    int answerPos = text.IndexOf("【答案】", contentStart);
                    if (answerPos > contentStart)
                    {
                        // 将到【答案】之前的内容作为一个不完整标签
                        string tagContent = text.Substring(contentStart, answerPos - contentStart);
                        tags.Add(new TagInfo 
                        { 
                            Start = tagStart, 
                            End = answerPos, 
                            Content = tagContent 
                        });
                    }
                    break;
                }
            }
            else
            {
                // 不是标签开始，退出循环
                break;
            }
        }
        
        return tags;
    }

    /// <summary>
    /// 在文本中查找标签结束位置
    /// </summary>
    /// <param name="text">文本</param>
    /// <param name="startPos">搜索开始位置</param>
    /// <returns>标签结束位置，找不到返回-1</returns>
    private int FindTagEndInText(string text, int startPos)
    {
        for (int i = startPos; i < text.Length; i++)
        {
            if (text[i] == ']' || text[i] == '］')
            {
                return i;
            }
        }
        return -1;
    }



    /// <summary>
    /// 清理重复的多选题标签
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <param name="questionNumber">题号</param>
    /// <param name="duplicateCount">重复标签数量</param>
    private void CleanupDuplicateMultipleChoiceTags(Paragraph paragraph, string questionNumber, int duplicateCount)
    {
        try
        {
            // 获取段落完整文本
            string fullText = GetParagraphFullText(paragraph);
            
            // 查找题号位置
            int questionEndPos = FindQuestionEndPosition(fullText, questionNumber);
            if (questionEndPos == -1) return;
            
            // 解析题号后的所有标签，使用与检查逻辑完全相同的方法
            var allTags = ParseTagsAfterQuestion(fullText, questionEndPos);
            
            // 筛选出所有多选题标签
            var multipleChoiceTags = allTags.Where(tag => tag.Content.Trim() == "多选题").ToList();
            
            // 如果多选题标签数量不符合预期或少于2个，直接返回
            if (multipleChoiceTags.Count != duplicateCount || multipleChoiceTags.Count < 2)
            {
                return;
            }
            
            // 保留第一个，删除其余的（从后往前删除，避免位置偏移）
            var tagsToRemove = multipleChoiceTags.Skip(1).OrderByDescending(tag => tag.Start).ToList();
            
            if (tagsToRemove.Count > 0)
            {
                // 逐个删除多余标签，每次删除后重新获取最新的段落状态
                foreach (var tag in tagsToRemove)
                {
                    // 每次删除前重新构建RunMap，确保位置信息准确
                    NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
                    if (runs.Count > 0)
                    {
                        // 重新获取当前段落的完整文本
                        string currentText = GetParagraphFullText(paragraph);
                        
                        // 重新查找题号位置
                        int currentQuestionEndPos = FindQuestionEndPosition(currentText, questionNumber);
                        if (currentQuestionEndPos == -1) break;
                        
                        // 重新解析当前的标签
                        var currentTags = ParseTagsAfterQuestion(currentText, currentQuestionEndPos);
                        var currentMultipleChoiceTags = currentTags.Where(t => t.Content.Trim() == "多选题").ToList();
                        
                        // 如果只剩1个或没有多选题标签了，就停止删除
                        if (currentMultipleChoiceTags.Count <= 1)
                        {
                            break;
                        }
                        
                        // 删除第二个多选题标签（保留第一个）
                        var tagToRemove = currentMultipleChoiceTags[1]; // 删除第二个
                        var runMap = BuildRunMap(runs);
                        RemoveTextFromRuns(runMap, tagToRemove.Start, tagToRemove.End);
                    }
                }
            }
        }
        catch
        {
            // 清理失败时不影响主流程
        }
    }

    /// <summary>
    /// 构建Run映射表
    /// </summary>
    /// <param name="runs">Run集合</param>
    /// <returns>Run映射列表</returns>
    private List<(Run run, string text, int globalStart, int globalEnd)> BuildRunMap(NodeCollection runs)
    {
        var runMap = new List<(Run run, string text, int globalStart, int globalEnd)>();
        int globalPos = 0;
        
        foreach (Run run in runs)
        {
            int start = globalPos;
            int end = globalPos + run.Text.Length;
            runMap.Add((run, run.Text, start, end));
            globalPos = end;
        }
        
        return runMap;
    }

    /// <summary>
    /// 从Run中删除指定范围的文本
    /// </summary>
    /// <param name="runMap">Run映射表</param>
    /// <param name="globalStart">全局开始位置</param>
    /// <param name="globalEnd">全局结束位置</param>
    private void RemoveTextFromRuns(List<(Run run, string text, int globalStart, int globalEnd)> runMap, 
                                   int globalStart, int globalEnd)
    {
        foreach (var (run, text, runStart, runEnd) in runMap)
        {
            // 检查当前Run是否与删除范围有交集
            if (runEnd <= globalStart || runStart >= globalEnd)
            {
                continue; // 无交集，跳过
            }
            
            // 计算在当前Run中的删除范围
            int deleteStart = Math.Max(0, globalStart - runStart);
            int deleteEnd = Math.Min(text.Length, globalEnd - runStart);
            
            if (deleteStart >= deleteEnd)
            {
                continue;
            }
            
            // 执行删除操作
            if (deleteStart == 0 && deleteEnd == text.Length)
            {
                // 删除整个Run的文本
                run.Text = "";
            }
            else if (deleteStart == 0)
            {
                // 删除Run开头部分
                run.Text = text.Substring(deleteEnd);
            }
            else if (deleteEnd == text.Length)
            {
                // 删除Run结尾部分
                run.Text = text.Substring(0, deleteStart);
            }
            else
            {
                // 删除Run中间部分
                run.Text = text.Substring(0, deleteStart) + text.Substring(deleteEnd);
            }
        }
    }



    /// <summary>
    /// 处理段落中的Run节点，合并相邻Run处理题号分割问题
    /// </summary>
    private void ProcessRunsForInsertion(Paragraph paragraph, string questionNumber, string targetPattern)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        if (runs.Count == 0) return;
        
        // 尝试不同的句号格式
        string[] patterns = { questionNumber + "．", questionNumber + ".", questionNumber + "。" };
        
        foreach (string pattern in patterns)
        {
            // 方法1：检查单个Run是否包含完整的题号+句号
            foreach (Run run in runs)
            {
                if (run.Text.Contains(pattern))
                {
                    InsertTagInSingleRun(run, pattern);
                    return;
                }
            }
            
            // 方法2：合并相邻Run的文本检查是否包含题号
            if (TryInsertTagInMergedRuns(runs, pattern))
            {
                return;
            }
        }
    }

    /// <summary>
    /// 在单个Run中插入多选题标签，设置括号字体为宋体
    /// </summary>
    private void InsertTagInSingleRun(Run run, string pattern)
    {
        string tagText = "［多选题］";
        string newText = run.Text.Replace(pattern, pattern + tagText);
        
        // 如果替换后的文本与原文本相同，说明没有替换成功
        if (newText == run.Text)
            return;
        
        // 查找插入位置
        int insertIndex = run.Text.IndexOf(pattern) + pattern.Length;
        
        // 分割Run：原文本 + 标签
        string beforeTag = run.Text.Substring(0, insertIndex);
        string afterTag = run.Text.Substring(insertIndex);
        
        // 保存原始字体信息
        string originalFontName = run.Font.Name;
        string originalFontNameFarEast = run.Font.NameFarEast;
        double originalFontSize = run.Font.Size;
        bool originalBold = run.Font.Bold;
        bool originalItalic = run.Font.Italic;
        
        // 修改当前Run为前半部分
        run.Text = beforeTag;
        
        // 创建标签Run，设置括号字体为宋体
        Run tagRun = new Run(run.Document);
        tagRun.Text = tagText;
        tagRun.Font.Name = "宋体";  // 设置括号字体为宋体
        tagRun.Font.NameFarEast = "宋体";  // 设置远东字体为宋体
        
        // 创建后半部分Run
        Run afterRun = new Run(run.Document);
        afterRun.Text = afterTag;
        // 恢复原始字体属性
        afterRun.Font.Name = originalFontName;
        afterRun.Font.NameFarEast = originalFontNameFarEast;
        afterRun.Font.Size = originalFontSize;
        afterRun.Font.Bold = originalBold;
        afterRun.Font.Italic = originalItalic;
        
        // 插入新的Run节点
        run.ParentNode.InsertAfter(tagRun, run);
        if (!string.IsNullOrEmpty(afterTag))
        {
            run.ParentNode.InsertAfter(afterRun, tagRun);
        }
    }

    /// <summary>
    /// 尝试在合并的Run文本中找到题号并插入标签
    /// </summary>
    private bool TryInsertTagInMergedRuns(NodeCollection runs, string targetPattern)
    {
        for (int i = 0; i < runs.Count; i++)
        {
            Run currentRun = (Run)runs[i];
            string mergedText = currentRun.Text;
            
            // 向后合并最多3个相邻Run的文本
            List<Run> involvedRuns = new List<Run> { currentRun };
            for (int j = i + 1; j < Math.Min(i + 4, runs.Count); j++)
            {
                Run nextRun = (Run)runs[j];
                mergedText += nextRun.Text;
                involvedRuns.Add(nextRun);
                
                // 检查合并后的文本是否包含目标模式
                if (mergedText.Contains(targetPattern))
                {
                    // 找到了，需要在正确的位置插入标签
                    return InsertTagInMergedRuns(involvedRuns, targetPattern);
                }
            }
        }
        return false;
    }

    /// <summary>
    /// 在合并的Run组中插入标签
    /// </summary>
    private bool InsertTagInMergedRuns(List<Run> runs, string targetPattern)
    {
        // 构建完整文本和Run映射
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        // 查找目标模式的位置
        int patternIndex = fullText.IndexOf(targetPattern);
        if (patternIndex == -1) return false;
        
        int insertPosition = patternIndex + targetPattern.Length;
        
        // 找到插入位置对应的Run
        foreach (var (start, end, run) in runMap)
        {
            if (insertPosition >= start && insertPosition <= end)
            {
                // 在这个Run中插入标签，使用分割方式设置字体
                int runOffset = insertPosition - start;
                string beforeTag = run.Text.Substring(0, runOffset);
                string afterTag = run.Text.Substring(runOffset);
                
                // 保存原始字体信息
                string originalFontName = run.Font.Name;
                string originalFontNameFarEast = run.Font.NameFarEast;
                double originalFontSize = run.Font.Size;
                bool originalBold = run.Font.Bold;
                bool originalItalic = run.Font.Italic;
                
                // 修改当前Run为前半部分
                run.Text = beforeTag;
                
                // 创建标签Run，设置括号字体为宋体
                Run tagRun = new Run(run.Document);
                tagRun.Text = "［多选题］";
                tagRun.Font.Name = "宋体";
                tagRun.Font.NameFarEast = "宋体";
                
                // 创建后半部分Run
                Run afterRun = new Run(run.Document);
                afterRun.Text = afterTag;
                // 恢复原始字体属性
                afterRun.Font.Name = originalFontName;
                afterRun.Font.NameFarEast = originalFontNameFarEast;
                afterRun.Font.Size = originalFontSize;
                afterRun.Font.Bold = originalBold;
                afterRun.Font.Italic = originalItalic;
                
                // 插入新的Run节点
                run.ParentNode.InsertAfter(tagRun, run);
                if (!string.IsNullOrEmpty(afterTag))
                {
                    run.ParentNode.InsertAfter(afterRun, tagRun);
                }
                
                return true;
            }
        }
        
        return false;
    }
} 