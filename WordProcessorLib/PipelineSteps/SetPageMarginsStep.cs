using Aspose.Words;
using WordProcessorLib.Interfaces;

// ==============================================
// SetPageMarginsStep.cs - 页边距设置管道步骤实现
// ==============================================

namespace WordProcessorLib.PipelineSteps;

public class SetPageMarginsStep : IPipelineStep
{
    private readonly double _topMargin;
    private readonly double _bottomMargin;
    private readonly double _leftMargin;
    private readonly double _rightMargin;
    private readonly double _headerDistance;
    private readonly double _footerDistance;
    
    public SetPageMarginsStep(double topMargin, double bottomMargin, double leftMargin,
                              double rightMargin, double headerDistance, double footerDistance)
    {
        _topMargin = topMargin;
        _bottomMargin = bottomMargin;
        _leftMargin = leftMargin;
        _rightMargin = rightMargin;
        _headerDistance = headerDistance;
        _footerDistance = footerDistance;
    }
    
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历文档的所有节（Section），确保全文档统一设置
            // Word 文档可能包含多个节，每个节可独立设置页面属性
            foreach (Section section in doc.Sections)
            {
                // ======================== 设置页面边距（单位：磅）========================
                section.PageSetup.TopMargin = _topMargin;              // 设置上边距
                section.PageSetup.BottomMargin = _bottomMargin;        // 设置下边距
                section.PageSetup.LeftMargin = _leftMargin;            // 设置左边距
                section.PageSetup.RightMargin = _rightMargin;          // 设置右边距
                
                section.PageSetup.HeaderDistance = _headerDistance;    // 设置页眉顶端距离
                section.PageSetup.FooterDistance = _footerDistance;    // 设置页脚底端距离
            }
            return true;
        }
        catch (Exception)
        {
            // 捕获任何异常（如文档损坏、权限问题等）
            // 返回 false 表示当前步骤失败，管道处理将终止并抛出异常
            return false;
        }
    }
}