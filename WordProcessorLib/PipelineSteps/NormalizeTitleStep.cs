using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 标题字体粗细设置选项
/// </summary>
public enum TitleFontWeight
{
    /// <summary>
    /// 不改变字体粗细
    /// </summary>
    Unchanged,
    
    /// <summary>
    /// 设置为粗体
    /// </summary>
    Bold,
    
    /// <summary>
    /// 设置为细体（非粗体）
    /// </summary>
    Normal
}

/// <summary>
/// 标准化标题处理步骤（实现IPipelineStep接口）
/// 功能：识别标题段落，在标题最后一段和后续内容之间设置指定空行数，并可设置标题字体粗细和字体大小标准化
/// 标题识别逻辑完全沿用SetFontsStep中的相关逻辑
/// 字体大小标准化：将标题中所有字符和段落分隔符的字体大小设置为标题中第一个字符的字体大小
/// </summary>
public class NormalizeTitleStep : IPipelineStep
{
    // 字段声明
    private readonly int _targetSpaceCount;          // 目标空行数
    private readonly TitleFontWeight _fontWeight;    // 标题字体粗细设置

    /// <summary>
    /// 构造函数：初始化目标空行数和字体粗细设置
    /// </summary>
    /// <param name="targetSpaceCount">目标空行数</param>
    /// <param name="fontWeight">标题字体粗细设置（Bold=粗体, Normal=细体, Unchanged=不变）</param>
    public NormalizeTitleStep(int targetSpaceCount, TitleFontWeight fontWeight = TitleFontWeight.Unchanged)
    {
        _targetSpaceCount = Math.Max(0, targetSpaceCount); // 确保不为负数
        _fontWeight = fontWeight;
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <param name="filePath">文件路径</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 识别标题段落（使用局部变量确保线程安全）
            var titleParagraphs = IdentifyTitleParagraphs(doc);

            // 如果没有识别到标题，直接返回
            if (titleParagraphs.Count == 0)
                return true;

            // 处理标题字体粗细和字体大小标准化
            ProcessTitleFontWeight(titleParagraphs);

            // 处理每个标题区域的间距
            ProcessTitleSpacing(titleParagraphs);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 识别文档中的标题段落
    /// 标题特征：文档最前面的几行，居中对齐，以空行结束
    /// 扩展：每个section开头的连续居中段落也被视为标题
    /// 扩展：每个页面开头（分页符后）的连续居中段落也被视为标题
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>识别到的标题段落集合</returns>
    private HashSet<Paragraph> IdentifyTitleParagraphs(Document doc)
    {
        var titleParagraphs = new HashSet<Paragraph>();

        // 获取文档中所有非表格段落
        var paragraphs = doc.GetChildNodes(NodeType.Paragraph, true)
            .Cast<Paragraph>()
            .Where(p => !IsInHeaderFooter(p))
            .ToList();

        if (paragraphs.Count == 0) return titleParagraphs;

        // 1. 处理文档开头的标题段落（原有逻辑）
        IdentifyConsecutiveCenteredParagraphs(paragraphs, 0, titleParagraphs);

        // 2. 处理每个section开头的标题段落
        IdentifyTitlesAtSectionStarts(paragraphs, titleParagraphs);

        // 3. 处理每个分页符后的标题段落
        IdentifyTitlesAfterPageBreaks(paragraphs, titleParagraphs);

        return titleParagraphs;
    }

    /// <summary>
    /// 识别从指定位置开始的连续居中段落作为标题
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">开始检查的索引位置</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyConsecutiveCenteredParagraphs(List<Paragraph> paragraphs, int startIndex, HashSet<Paragraph> titleParagraphs)
    {
        if (startIndex >= paragraphs.Count) return;

        for (int i = startIndex; i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];

            // 检查是否为居中对齐
            if (para.ParagraphFormat.Alignment == ParagraphAlignment.Center)
            {
                titleParagraphs.Add(para);

                // 如果是空段落且居中，认为标题区域结束
                if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
                {
                    break;
                }
            }
            else
            {
                // 遇到非居中段落，标题区域结束
                break;
            }
        }
    }

    /// <summary>
    /// 识别每个section开头的标题段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyTitlesAtSectionStarts(List<Paragraph> paragraphs, HashSet<Paragraph> titleParagraphs)
    {
        // 遍历所有段落，找到每个section的第一个段落
        for (int i = 1; i < paragraphs.Count; i++) // 从1开始，因为第0个段落已经在文档开头处理过了
        {
            Paragraph para = paragraphs[i];

            // 检查当前段落是否是section的第一个段落
            if (IsFirstParagraphInSection(para))
            {
                // 从这个段落开始识别连续的居中段落作为标题
                IdentifyConsecutiveCenteredParagraphs(paragraphs, i, titleParagraphs);
            }
        }
    }

    /// <summary>
    /// 识别每个分页符后的标题段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyTitlesAfterPageBreaks(List<Paragraph> paragraphs, HashSet<Paragraph> titleParagraphs)
    {
        // 遍历所有段落，找到每个分页符后的第一个段落
        for (int i = 1; i < paragraphs.Count; i++) // 从1开始，因为第0个段落已经在文档开头处理过了
        {
            Paragraph para = paragraphs[i];
            Paragraph prevPara = paragraphs[i - 1];

            // 检查前一个段落是否有分页符
            if (HasPageBreakBefore(para) || HasPageBreakAfter(prevPara))
            {
                // 从这个段落开始识别连续的居中段落作为标题
                IdentifyConsecutiveCenteredParagraphs(paragraphs, i, titleParagraphs);
            }
        }
    }

    /// <summary>
    /// 处理标题间距
    /// </summary>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void ProcessTitleSpacing(HashSet<Paragraph> titleParagraphs)
    {
        // 按标题区域分组处理
        var titleGroups = GroupTitlesByRegion(titleParagraphs);

        foreach (var titleGroup in titleGroups)
        {
            ProcessSingleTitleGroup(titleGroup);
        }
    }

    /// <summary>
    /// 将标题段落按区域分组
    /// </summary>
    /// <param name="titleParagraphs">标题段落集合</param>
    /// <returns>标题区域分组</returns>
    private List<List<Paragraph>> GroupTitlesByRegion(HashSet<Paragraph> titleParagraphs)
    {
        var groups = new List<List<Paragraph>>();
        var currentGroup = new List<Paragraph>();

        // 获取所有标题段落并按文档顺序排序
        var sortedTitles = titleParagraphs
            .OrderBy(p => GetParagraphPosition(p))
            .ToList();
        
        foreach (var title in sortedTitles)
        {
            if (currentGroup.Count == 0)
            {
                currentGroup.Add(title);
            }
            else
            {
                // 检查是否与前一个标题连续
                var lastTitle = currentGroup.Last();
                if (AreConsecutiveTitles(lastTitle, title))
                {
                    currentGroup.Add(title);
                }
                else
                {
                    // 开始新的标题组
                    groups.Add(currentGroup);
                    currentGroup = new List<Paragraph> { title };
                }
            }
        }
        
        if (currentGroup.Count > 0)
        {
            groups.Add(currentGroup);
        }
        
        return groups;
    }

    /// <summary>
    /// 处理单个标题组的间距
    /// </summary>
    /// <param name="titleGroup">标题组</param>
    private void ProcessSingleTitleGroup(List<Paragraph> titleGroup)
    {
        if (titleGroup.Count == 0) return;
        
        // 获取标题组的最后一个段落
        var lastTitleParagraph = titleGroup.Last();
        
        // 检查最后一个标题段落是否是空行
        // 如果是空行，说明它是标题后的分隔空行，应该被替换而不是保留
        if (IsEmptyParagraph(lastTitleParagraph))
        {
            // 如果标题组只有一个空段落，这不是有效的标题组，跳过处理
            if (titleGroup.Count == 1)
                return;
                
            // 获取倒数第二个段落作为真正的最后标题段落
            var realLastTitleParagraph = titleGroup[titleGroup.Count - 2];
            
            // 删除这个空行段落
            lastTitleParagraph.Remove();
            
            // 删除真正最后标题段落后的现有空行
            RemoveSpacesAfterTitle(realLastTitleParagraph);
            
            // 添加指定数量的标准化空行
            AddStandardizedSpaces(realLastTitleParagraph, _targetSpaceCount);
        }
        else
        {
            // 最后一个段落不是空行，按正常逻辑处理
            // 删除标题后的现有空行
            RemoveSpacesAfterTitle(lastTitleParagraph);
            
            // 添加指定数量的标准化空行
            AddStandardizedSpaces(lastTitleParagraph, _targetSpaceCount);
        }
    }

    /// <summary>
    /// 删除标题后的现有空行
    /// </summary>
    /// <param name="lastTitleParagraph">标题组的最后一个段落</param>
    private void RemoveSpacesAfterTitle(Paragraph lastTitleParagraph)
    {
        var parent = lastTitleParagraph.ParentNode;
        if (parent == null) return;

        var siblings = parent.GetChildNodes(NodeType.Any, false);
        int titleIndex = siblings.IndexOf(lastTitleParagraph);

        if (titleIndex < 0) return;

        // 收集标题后的空行段落
        var spacesToRemove = new List<Node>();
        for (int i = titleIndex + 1; i < siblings.Count; i++)
        {
            var node = siblings[i];
            if (node is Paragraph para && IsEmptyParagraph(para))
            {
                spacesToRemove.Add(node);
            }
            else
            {
                // 遇到非空段落，停止删除
                break;
            }
        }

        // 删除空行
        foreach (var space in spacesToRemove)
        {
            space.Remove();
        }
    }

    /// <summary>
    /// 添加标准化的空行
    /// </summary>
    /// <param name="lastTitleParagraph">标题组的最后一个段落</param>
    /// <param name="spaceCount">空行数量</param>
    private void AddStandardizedSpaces(Paragraph lastTitleParagraph, int spaceCount)
    {
        if (spaceCount <= 0) return;

        var doc = lastTitleParagraph.Document;
        var parent = lastTitleParagraph.ParentNode;

        for (int i = 0; i < spaceCount; i++)
        {
            // 创建空行段落，格式与标题一致
            var emptyPara = CreateStandardizedEmptyParagraph(doc, lastTitleParagraph);

            // 插入到标题后面
            parent.InsertAfter(emptyPara, lastTitleParagraph);

            // 更新插入位置引用
            lastTitleParagraph = emptyPara;
        }
    }

    /// <summary>
    /// 创建标准化的空行段落，格式与标题一致
    /// </summary>
    /// <param name="documentBase">文档对象</param>
    /// <param name="titleParagraph">参考标题段落</param>
    /// <returns>标准化的空行段落</returns>
    private Paragraph CreateStandardizedEmptyParagraph(DocumentBase documentBase, Paragraph titleParagraph)
    {
        var emptyPara = new Paragraph(documentBase);

        // 复制标题段落的格式
        emptyPara.ParagraphFormat.FirstLineIndent = titleParagraph.ParagraphFormat.FirstLineIndent;
        emptyPara.ParagraphFormat.Alignment = titleParagraph.ParagraphFormat.Alignment; // 居中对齐

        // 复制其他段落格式属性
        emptyPara.ParagraphFormat.LeftIndent = titleParagraph.ParagraphFormat.LeftIndent;
        emptyPara.ParagraphFormat.RightIndent = titleParagraph.ParagraphFormat.RightIndent;
        emptyPara.ParagraphFormat.SpaceBefore = titleParagraph.ParagraphFormat.SpaceBefore;
        emptyPara.ParagraphFormat.SpaceAfter = titleParagraph.ParagraphFormat.SpaceAfter;
        emptyPara.ParagraphFormat.LineSpacing = titleParagraph.ParagraphFormat.LineSpacing;

        // 获取标题段落的标准字体大小
        double standardFontSize = GetFirstCharacterFontSize(titleParagraph);

        // 设置段落换行符字体格式，与标题一致
        if (titleParagraph.Runs.Count > 0)
        {
            var firstRun = titleParagraph.Runs[0];
            emptyPara.ParagraphBreakFont.Name = firstRun.Font.Name;
            emptyPara.ParagraphBreakFont.NameFarEast = firstRun.Font.NameFarEast;
            // 使用标准化的字体大小
            emptyPara.ParagraphBreakFont.Size = standardFontSize > 0 ? standardFontSize : firstRun.Font.Size;

            // 根据字体粗细设置决定是否设置粗体
            emptyPara.ParagraphBreakFont.Bold = _fontWeight switch
            {
                TitleFontWeight.Bold => true,
                TitleFontWeight.Normal => false,
                TitleFontWeight.Unchanged => firstRun.Font.Bold,
                _ => firstRun.Font.Bold
            };
        }
        else
        {
            // 如果标题段落没有Run，使用段落换行符字体
            emptyPara.ParagraphBreakFont.Name = titleParagraph.ParagraphBreakFont.Name;
            emptyPara.ParagraphBreakFont.NameFarEast = titleParagraph.ParagraphBreakFont.NameFarEast;
            // 使用标准化的字体大小
            emptyPara.ParagraphBreakFont.Size = standardFontSize > 0 ? standardFontSize : titleParagraph.ParagraphBreakFont.Size;

            // 根据字体粗细设置决定是否设置粗体
            emptyPara.ParagraphBreakFont.Bold = _fontWeight switch
            {
                TitleFontWeight.Bold => true,
                TitleFontWeight.Normal => false,
                TitleFontWeight.Unchanged => titleParagraph.ParagraphBreakFont.Bold,
                _ => titleParagraph.ParagraphBreakFont.Bold
            };
        }

        return emptyPara;
    }

    /// <summary>
    /// 处理标题字体粗细和字体大小标准化
    /// </summary>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void ProcessTitleFontWeight(HashSet<Paragraph> titleParagraphs)
    {
        // 按标题区域分组处理，确保每个标题组使用统一的字体大小
        var titleGroups = GroupTitlesByRegion(titleParagraphs);

        foreach (var titleGroup in titleGroups)
        {
            // 获取整个标题组的标准字体大小
            double standardFontSize = GetTitleGroupStandardFontSize(titleGroup);

            foreach (var titleParagraph in titleGroup)
            {
                // 处理段落中的所有Run
                foreach (Node node in titleParagraph.GetChildNodes(NodeType.Run, true))
                {
                    if (node is Run run)
                    {
                        // 只有在非Unchanged模式下才设置字体粗细
                        if (_fontWeight != TitleFontWeight.Unchanged)
                        {
                            bool targetBold = _fontWeight == TitleFontWeight.Bold;
                            run.Font.Bold = targetBold;
                        }
                        
                        // 标准化字体大小（总是执行）
                        if (standardFontSize > 0)
                        {
                            run.Font.Size = standardFontSize;
                        }
                    }
                }

                // 处理段落换行符字体
                if (_fontWeight != TitleFontWeight.Unchanged)
                {
                    bool targetBold = _fontWeight == TitleFontWeight.Bold;
                    titleParagraph.ParagraphBreakFont.Bold = targetBold;
                }
                
                // 标准化段落换行符字体大小（总是执行）
                if (standardFontSize > 0)
                {
                    titleParagraph.ParagraphBreakFont.Size = standardFontSize;
                }
            }
        }
    }

    /// <summary>
    /// 获取标题组的标准字体大小
    /// </summary>
    /// <param name="titleGroup">标题组</param>
    /// <returns>标题组的标准字体大小，如果没有找到则返回0</returns>
    private double GetTitleGroupStandardFontSize(List<Paragraph> titleGroup)
    {
        // 遍历标题组中的所有段落，找到第一个包含文本的字符
        foreach (var titleParagraph in titleGroup)
        {
            // 跳过空段落，查找有文本内容的段落
            if (IsEmptyParagraph(titleParagraph))
                continue;

            // 获取此段落中第一个字符的字体大小
            double fontSize = GetFirstCharacterFontSize(titleParagraph);
            if (fontSize > 0)
                return fontSize;
        }

        // 如果所有段落都是空的，尝试从第一个段落的ParagraphBreakFont获取
        if (titleGroup.Count > 0)
        {
            var firstPara = titleGroup[0];
            if (firstPara.ParagraphBreakFont.Size > 0)
                return firstPara.ParagraphBreakFont.Size;
        }

        // 如果都没有找到，返回0表示无效
        return 0;
    }

    /// <summary>
    /// 获取标题段落中第一个字符的字体大小
    /// </summary>
    /// <param name="titleParagraph">标题段落</param>
    /// <returns>第一个字符的字体大小，如果没有找到则返回0</returns>
    private double GetFirstCharacterFontSize(Paragraph titleParagraph)
    {
        // 遍历段落中的所有Run，找到第一个包含可见文本的Run
        foreach (Node node in titleParagraph.GetChildNodes(NodeType.Run, true))
        {
            if (node is Run run && !string.IsNullOrEmpty(run.Text))
            {
                // 检查Run的文本中是否有非空白字符
                string trimmedText = run.Text.Trim();
                if (!string.IsNullOrEmpty(trimmedText))
                {
                    // 返回第一个有可见文本的Run的字体大小
                    return run.Font.Size;
                }
            }
        }

        // 如果没有找到包含可见文本的Run，检查段落换行符字体大小
        if (titleParagraph.ParagraphBreakFont.Size > 0)
        {
            return titleParagraph.ParagraphBreakFont.Size;
        }

        // 如果都没有找到，返回0表示无效
        return 0;
    }

    /// <summary>
    /// 检查段落是否是section的第一个段落
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否是section的第一个段落</returns>
    private bool IsFirstParagraphInSection(Paragraph para)
    {
        // 获取段落所在的section
        Section section = para.ParentSection;
        if (section == null) return false;

        // 获取section的第一个段落
        Body body = section.Body;
        if (body == null || body.FirstParagraph == null) return false;

        // 检查当前段落是否是section的第一个段落
        return para == body.FirstParagraph;
    }

    /// <summary>
    /// 检查段落是否有分页符前缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符前缀</returns>
    private bool HasPageBreakBefore(Paragraph para)
    {
        return para.ParagraphFormat.PageBreakBefore;
    }

    /// <summary>
    /// 检查段落是否有分页符后缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符后缀</returns>
    private bool HasPageBreakAfter(Paragraph para)
    {
        // 检查段落中是否包含分页符
        string text = para.ToString(SaveFormat.Text);

        // 检查是否有分页符字符
        if (text.Contains("\f"))
            return true;

        // 检查段落中的所有Run
        foreach (Node node in para.GetChildNodes(NodeType.Run, true))
        {
            if (node is Run run && run.Text != null && run.Text.Contains("\f"))
                return true;
        }

        // 检查是否有分页符
        return false;
    }

    /// <summary>
    /// 检查段落是否在页眉页脚中
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否在页眉页脚中</returns>
    private static bool IsInHeaderFooter(Paragraph para)
    {
        return para.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 判断段落是否为空（无文本且无图片）
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否为空段落</returns>
    private bool IsEmptyParagraph(Paragraph para)
    {
        // 检查是否有非空白文本
        if (!string.IsNullOrWhiteSpace(para.GetText().Trim()))
            return false;

        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否包含图片或形状</returns>
    private bool HasImageOrShape(Paragraph para)
    {
        // 检查Shape节点（包含图片和其他形状）
        foreach (Node node in para.GetChildNodes(NodeType.Shape, true))
        {
            if (node is Shape shape && shape.ImageData.HasImage)
                return true;
        }

        // 检查GroupShape节点（组合形状）
        if (para.GetChildNodes(NodeType.GroupShape, true).Count > 0)
            return true;

        // 检查OfficeMath节点（数学公式）
        if (para.GetChildNodes(NodeType.OfficeMath, true).Count > 0)
            return true;

        return false;
    }

    /// <summary>
    /// 获取段落在文档中的位置
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>段落位置</returns>
    private int GetParagraphPosition(Paragraph para)
    {
        var doc = para.Document;
        var allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
        return allParagraphs.IndexOf(para);
    }

    /// <summary>
    /// 检查两个段落是否连续
    /// </summary>
    /// <param name="para1">第一个段落</param>
    /// <param name="para2">第二个段落</param>
    /// <returns>是否连续</returns>
    private bool AreConsecutiveTitles(Paragraph para1, Paragraph para2)
    {
        // 检查两个段落是否在同一个父节点中
        if (para1.ParentNode != para2.ParentNode)
            return false;

        // 获取所有兄弟节点
        var siblings = para1.ParentNode.GetChildNodes(NodeType.Any, false);
        int index1 = siblings.IndexOf(para1);
        int index2 = siblings.IndexOf(para2);

        // 检查索引是否连续
        return Math.Abs(index1 - index2) == 1;
    }
}
