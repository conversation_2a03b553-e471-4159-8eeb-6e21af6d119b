using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;
using System.Text.RegularExpressions;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 设置题号前置空间步骤（完整实现版）
/// 功能：精确调整题号段落前的空行数，并标准化空行格式
/// 题号格式：段落开头的数字+中文点"．"
/// 支持跳过指定字段：指定字段所在段落与题号要连在一起，中间不能有空行
/// 跳过字段包括：skipFields列表 + targetFields字典的所有值
/// </summary>
public class SetQuestionSpacesStep : IPipelineStep
{
    private readonly int _requiredSpaces;
    private readonly List<string> _skipFields;
    private readonly List<string> _targetFieldValues;

    /// <summary>
    /// 构造函数初始化参数
    /// </summary>
    /// <param name="requiredSpaces">需要的标准空行数（≥0）</param>
    /// <param name="skipFields">需要跳过的指定字段列表，这些字段所在段落与题号要连在一起</param>
    /// <param name="targetFields">目标字段列表，其值也作为指定字段的识别范围</param>
    /// <exception cref="ArgumentException">空行数为负时抛出</exception>
    public SetQuestionSpacesStep(int requiredSpaces, List<string>? skipFields = null, List<string>? targetFields = null)
    {
        // 参数有效性验证
        _requiredSpaces = requiredSpaces >= 0 ? requiredSpaces
            : throw new ArgumentException("空行数不能为负数", nameof(requiredSpaces));
        _skipFields = skipFields ?? new List<string>();
        _targetFieldValues = targetFields ?? new List<string>();
    }

    /// <summary>
    /// 实现接口核心方法
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body);
            }
            return true;
        }
        catch (Exception ex)
        {
            // 记录详细错误信息以便调试
            Console.WriteLine($"SetQuestionSpacesStep 执行失败: {ex.Message}");
            Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            return false;
        }
    }

    /// <summary>
    /// 处理单个正文区域
    /// </summary>
    private void ProcessBody(Body body)
    {
        // 获取所有普通段落（排除页眉页脚和表格内段落）
        List<Paragraph> paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null && !IsInTable(p))
            .ToList();

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsQuestionParagraph(current))
            {
                AdjustSpacesBefore(current);
            }
        }
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    private bool IsInTable(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }
    
    /// <summary>
    /// 判断是否为题号段落（严格匹配数字+中文点"．"格式）
    /// </summary>
    private bool IsQuestionParagraph(Paragraph para)
    {
        string? text = para.Range?.Text?.TrimStart();
        if (string.IsNullOrEmpty(text))
            return false;

        // 严格匹配题号格式：段落开头的数字+中文点"．"
        Regex questionPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
        return questionPattern.IsMatch(text);
    }

    /// <summary>
    /// 调整题号段落前的空行（支持跳过指定字段）
    /// </summary>
    private void AdjustSpacesBefore(Paragraph target)
    {
        if (target?.ParentNode == null)
        {
            return; // 安全检查：如果目标段落或其父节点为空，直接返回
        }

        CompositeNode parent = target.ParentNode;
        NodeCollection siblings = parent.GetChildNodes(NodeType.Any, false);
        int targetIndex = siblings.IndexOf(target);

        if (targetIndex < 0)
        {
            return; // 安全检查：如果找不到目标段落的索引，直接返回
        }

        // 找到实际应该插入空行的位置（跳过指定字段）
        var spaceInsertionInfo = FindSpaceInsertionPoint(siblings, targetIndex);

        // 删除所有现有空行（避免格式不统一问题）
        RemoveSpaces(spaceInsertionInfo.ExistingSpaces, spaceInsertionInfo.ExistingSpaces.Count);

        // 重新获取插入点，因为删除操作可能影响了节点结构
        Node finalInsertionPoint = GetValidInsertionPoint(parent, target, spaceInsertionInfo.InsertionPoint);

        // 在正确的位置添加所需数量的新空行（统一格式，克隆题号段落格式）
        AddSpaces(parent, finalInsertionPoint, _requiredSpaces);
    }

    /// <summary>
    /// 空行插入信息
    /// </summary>
    private class SpaceInsertionInfo
    {
        public Node InsertionPoint { get; set; } = null!;
        public List<Node> ExistingSpaces { get; set; } = new List<Node>();
    }

    /// <summary>
    /// 找到空行插入点（跳过指定字段）
    /// </summary>
    private SpaceInsertionInfo FindSpaceInsertionPoint(NodeCollection siblings, int targetIndex)
    {
        var result = new SpaceInsertionInfo();
        var existingSpaces = new List<Node>();

        // 从题号段落开始往前查找
        int currentIndex = targetIndex - 1;
        Node insertionPoint = siblings[targetIndex]; // 默认插入点为题号段落前

        while (currentIndex >= 0)
        {
            Node currentNode = siblings[currentIndex];

            if (currentNode is Paragraph currentPara)
            {
                if (IsSpaceParagraph(currentPara))
                {
                    // 遇到空段落，收集并继续往前
                    existingSpaces.Add(currentNode);
                    currentIndex--;
                }
                else if (IsSkipFieldParagraph(currentPara))
                {
                    // 遇到指定字段段落，跳过并继续往前查找
                    currentIndex--;
                }
                else
                {
                    // 遇到其他有内容的段落，在此处设置插入点
                    insertionPoint = siblings[currentIndex + 1];
                    break;
                }
            }
            else
            {
                // 遇到非段落节点，在此处设置插入点
                insertionPoint = siblings[currentIndex + 1];
                break;
            }
        }

        // 如果查找到了文档开头，插入点应该是题号段落前
        if (currentIndex < 0)
        {
            // 无论是否有现有空段落，插入点都应该是题号段落前
            // 因为现有空段落会被删除，所以不能以它们作为插入点
            insertionPoint = siblings[targetIndex];
        }

        // 恢复空段落的原始顺序（从前往后）
        existingSpaces.Reverse();

        result.InsertionPoint = insertionPoint;
        result.ExistingSpaces = existingSpaces;
        return result;
    }

    /// <summary>
    /// 获取当前前置空行（包含非标准空行）
    /// </summary>
    private List<Node> GetExistingSpaces(NodeCollection siblings, int targetIndex)
    {
        List<Node> spaces = new List<Node>();
        if (targetIndex < 1) return spaces;

        // 向前查找所有连续空段落
        for (int i = targetIndex - 1; i >= 0; i--)
        {
            Node node = siblings[i];
            if (node is Paragraph p && IsSpaceParagraph(p))
            {
                spaces.Add(node);
            }
            else
            {
                break; // 遇到非空段落停止
            }
        }

        // 恢复原始顺序（从前往后）
        spaces.Reverse();
        return spaces;
    }

    /// <summary>
    /// 添加缺失的空行（克隆题号段落格式）
    /// </summary>
    private void AddSpaces(CompositeNode parent, Node insertionPoint, int count)
    {
        // 获取文档对象
        Document doc = parent.Document as Document;
        if (doc == null)
        {
            throw new InvalidOperationException("无法获取文档对象");
        }

        // 找到题号段落作为格式模板
        Paragraph? templateParagraph = null;
        if (insertionPoint is Paragraph para)
        {
            templateParagraph = para;
        }
        else
        {
            // 如果插入点不是段落，向后查找第一个段落作为模板
            Node? current = insertionPoint;
            while (current != null)
            {
                if (current is Paragraph p)
                {
                    templateParagraph = p;
                    break;
                }
                current = current.NextSibling;
            }
        }

        // 验证插入点是否是父节点的直接子节点
        if (insertionPoint.ParentNode != parent)
        {
            throw new InvalidOperationException($"插入点不是父节点的直接子节点。插入点父节点: {insertionPoint.ParentNode?.GetType().Name}, 期望父节点: {parent.GetType().Name}");
        }

        for (int i = 0; i < count; i++)
        {
            Paragraph space;
            if (templateParagraph != null)
            {
                // 克隆模板段落以保持格式一致
                space = (Paragraph)templateParagraph.Clone(false);
                space.RemoveAllChildren();
            }
            else
            {
                // 如果没有找到模板段落，创建新的空段落
                space = new Paragraph(doc);
            }

            space.AppendChild(new Run(doc, ""));
            parent.InsertBefore(space, insertionPoint);
        }
    }

    /// <summary>
    /// 获取有效的插入点（确保插入点仍然是父节点的子节点）
    /// </summary>
    private Node GetValidInsertionPoint(CompositeNode parent, Paragraph target, Node originalInsertionPoint)
    {
        // 如果原始插入点仍然有效，使用它
        if (originalInsertionPoint?.ParentNode == parent)
        {
            return originalInsertionPoint;
        }

        // 如果原始插入点无效，使用目标段落作为插入点
        if (target.ParentNode == parent)
        {
            return target;
        }

        // 如果目标段落也无效，抛出异常
        throw new InvalidOperationException("无法找到有效的插入点");
    }

    /// <summary>
    /// 删除多余的空行
    /// </summary>
    private void RemoveSpaces(List<Node> existingSpaces, int count)
    {
        // 从后往前删除（保持索引稳定）
        for (int i = 0; i < count && existingSpaces.Count > 0; i++)
        {
            Node lastSpace = existingSpaces.Last();
            lastSpace.Remove();
            existingSpaces.Remove(lastSpace);
        }
    }

    /// <summary>
    /// 判断是否为空行段落（包含各种空格）
    /// </summary>
    private bool IsSpaceParagraph(Paragraph para)
    {
        // 检查是否有非空白文本
        if (!string.IsNullOrWhiteSpace(para.GetText()))
            return false;
        
        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    private bool HasImageOrShape(Paragraph para)
    {
        // 直接搜索段落中的 Shape 节点（Aspose.Words.Drawing.Shape）
        foreach (Shape shape in para.GetChildNodes(NodeType.Shape, true))
        {
            if (shape.ImageData.HasImage)
                return true;
        }
        return false;
    }

    /// <summary>
    /// 判断段落是否为指定字段段落（段落只含有指定字段内容）
    /// 检查范围包括：skipFields 和 targetFields 的值
    /// </summary>
    private bool IsSkipFieldParagraph(Paragraph para)
    {
        if (_skipFields.Count == 0 && _targetFieldValues.Count == 0)
            return false;

        string? text = para.Range?.Text?.Trim();
        if (string.IsNullOrEmpty(text))
            return false;

        // 检查段落文本是否完全匹配任一指定字段（skipFields）
        bool matchesSkipFields = _skipFields.Any(field => string.Equals(text, field, StringComparison.Ordinal));

        // 检查段落文本是否完全匹配任一目标字段值（targetFields的值）
        bool matchesTargetFields = _targetFieldValues.Any(field => string.Equals(text, field, StringComparison.Ordinal));

        return matchesSkipFields || matchesTargetFields;
    }
}
