using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;
using System.Text;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 数学函数标准化处理步骤（实现IPipelineStep接口）
/// 功能：遍历文档的公式节点，将指定的数学函数字段设置为非斜体
/// 作用：确保数学函数（如sin、cos、tan等）在公式中显示为正体而非斜体，符合数学排版规范
/// </summary>
public class NormalizeMathFunctionStep : IPipelineStep
{
    // 字段声明
    private readonly List<string> _targetFunctions;  // 需要设置为非斜体的数学函数集合（使用List保证顺序）

    /// <summary>
    /// 构造函数：初始化目标数学函数配置
    /// </summary>
    /// <param name="targetFunctions">需要设置为非斜体的数学函数列表</param>
    /// <remarks>
    /// 使用List存储函数名并按长度降序排列，确保长函数名优先匹配，支持严格匹配（区分大小写）
    /// 例如：["sin", "cos", "tan"]
    /// </remarks>
    public NormalizeMathFunctionStep(List<string> targetFunctions)
    {
        // 将列表按长度降序排列，确保长函数名优先匹配
        _targetFunctions = targetFunctions != null
            ? targetFunctions.OrderByDescending(f => f.Length).ThenBy(f => f).ToList()
            : new List<string>();


    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的Word文档对象</param>
    /// <param name="filePath">文档文件路径（用于日志记录）</param>
    /// <returns>处理成功返回true，失败返回false</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 如果没有配置目标函数，直接返回成功
            if (_targetFunctions.Count == 0)
            {
                return true;
            }

            // 获取文档所有节点（深度递归遍历）
            NodeCollection nodes = doc.GetChildNodes(NodeType.Any, true);

            // 遍历处理每个节点
            foreach (Node node in nodes)
            {
                // 跳过页眉页脚内容
                if (IsInHeaderFooter(node)) continue;

                // 根据节点类型分发处理
                ProcessNode(node);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    /// <param name="node">当前文档节点</param>
    /// <returns>true：属于页眉页脚内容；false：属于正文内容</returns>
    /// <remarks>
    /// 通过查找最近的HeaderFooter祖先节点判断位置，确保只处理正文中的公式
    /// </remarks>
    private static bool IsInHeaderFooter(Node node)
    {
        // 通过查找最近的HeaderFooter祖先节点判断位置
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 节点处理路由中心
    /// </summary>
    /// <param name="node">当前文档节点</param>
    /// <remarks>
    /// 使用类型模式匹配分发处理，只处理OfficeMath类型的节点（微软公式节点）
    /// </remarks>
    private void ProcessNode(Node node)
    {
        // 使用类型模式匹配分发处理，只处理微软公式节点
        switch (node)
        {
            case OfficeMath mathNode:
                ProcessOfficeMath(mathNode);
                break;
        }
    }
    
    /// <summary>
    /// 处理微软公式节点，查找并设置指定数学函数为非斜体
    /// </summary>
    /// <param name="mathNode">微软公式节点</param>
    /// <remarks>
    /// 核心处理逻辑：
    /// 1. 获取公式中所有Run节点
    /// 2. 分析文本内容，查找目标函数
    /// 3. 处理跨Run的情况，智能合并文本进行匹配
    /// 4. 对匹配的函数设置非斜体格式
    /// </remarks>
    private void ProcessOfficeMath(OfficeMath mathNode)
    {
        // 获取公式中所有文本运行节点（深度遍历）
        NodeCollection runs = mathNode.GetChildNodes(NodeType.Run, true);

        // 创建需要处理的Run节点列表（避免在遍历过程中修改集合导致的问题）
        // 作用：将待处理的Run节点复制到一个独立的列表中
        // 必要性：非常必要，因为后续操作可能会修改节点结构，在遍历原集合时直接修改会导致集合被修改的异常
        List<Run> runList = new List<Run>();
        foreach (Run run in runs)
        {
            runList.Add(run);
            // Console.WriteLine($"DEBUG: {run.Text}={run.Font.Bold}");
        }

        // 处理跨Run的函数匹配
        ProcessCrossRunFunctions(runList);
    }



    /// <summary>
    /// 处理公式中的Run节点，查找并设置指定数学函数为非斜体
    /// </summary>
    /// <param name="runList">公式中的Run节点列表</param>
    /// <remarks>
    /// 按照用户思路实现：先处理单Run内的函数，再处理跨Run的函数
    /// 1. 第一步：遍历每个Run，处理单Run内的函数匹配
    /// 2. 第二步：处理跨Run的函数匹配
    /// </remarks>
    private void ProcessCrossRunFunctions(List<Run> runList)
    {
        if (runList.Count == 0) return;

        // 第一步：处理单Run内的函数匹配
        ProcessSingleRunFunctions(runList);

        // 第二步：处理跨Run的函数匹配
        ProcessCrossRunMatches(runList);
    }

    /// <summary>
    /// 处理单Run内的函数匹配
    /// 修复：确保所有匹配都基于原始文本，避免分割过程中的文本变化影响后续匹配
    /// </summary>
    private void ProcessSingleRunFunctions(List<Run> runList)
    {
        // 简单直接的方法：遍历每个Run，查找并分割函数
        for (int i = 0; i < runList.Count; i++)
        {
            var run = runList[i];
            string originalRunText = run.Text ?? "";
            if (string.IsNullOrEmpty(originalRunText)) continue;



            // 在原始文本中查找所有函数匹配
            var matches = FindFunctionMatchesInText(originalRunText);
            if (matches.Count == 0) continue;



            // 关键修复：如果有多个匹配，使用一次性分割方法
            if (matches.Count > 1)
            {
                SplitRunForMultipleFunctions(run, matches, originalRunText);
            }
            else
            {
                // 单个匹配，使用原有方法
                var match = matches[0];
                SplitRunForFunction(run, match.StartIndex, match.StartIndex + match.Length);
            }
        }
    }

    /// <summary>
    /// 处理跨Run的函数匹配
    /// </summary>
    private void ProcessCrossRunMatches(List<Run> runList)
    {
        // 重新获取当前段落的所有Run（因为第一步可能已经分割了一些Run）
        if (runList.Count == 0) return;

        var paragraph = runList[0].GetAncestor(NodeType.Paragraph) as Paragraph;
        if (paragraph == null) return;

        var currentRuns = paragraph.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();
        if (currentRuns.Count == 0) return;

        // 构建完整文本和Run映射
        var runMappings = BuildRunMappings(currentRuns);
        string fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));

        if (string.IsNullOrEmpty(fullText)) return;



        // 查找跨Run的函数匹配
        var crossRunMatches = FindCrossRunMatches(fullText, runMappings);



        // 处理每个跨Run匹配
        foreach (var match in crossRunMatches)
        {
            ProcessSingleCrossRunMatch(match, runMappings);
        }
    }

    /// <summary>
    /// 查找跨Run的函数匹配
    /// 关键修复：只处理真正跨Run的函数，避免重复处理单Run内的函数
    /// </summary>
    private List<FunctionMatch> FindCrossRunMatches(string fullText, List<RunMapping> runMappings)
    {
        var matches = new List<FunctionMatch>();

        foreach (string targetFunction in _targetFunctions)
        {
            int searchStart = 0;
            while (true)
            {
                int foundIndex = fullText.IndexOf(targetFunction, searchStart, StringComparison.Ordinal);
                if (foundIndex == -1) break;

                // 关键修复：只处理真正跨Run的函数匹配
                if (IsMatchCrossRun(foundIndex, targetFunction.Length, runMappings))
                {
                    matches.Add(new FunctionMatch
                    {
                        FunctionName = targetFunction,
                        StartIndex = foundIndex,
                        Length = targetFunction.Length
                    });
                }

                searchStart = foundIndex + 1;
            }
        }

        return matches.OrderBy(m => m.StartIndex).ThenByDescending(m => m.Length).ToList();
    }

    /// <summary>
    /// 检查匹配是否跨越多个Run
    /// </summary>
    private bool IsMatchCrossRun(int startIndex, int length, List<RunMapping> runMappings)
    {
        int endIndex = startIndex + length;

        // 查找包含起始位置的Run
        var startRun = runMappings.FirstOrDefault(rm =>
            rm.StartPosition <= startIndex && startIndex < rm.EndPosition);

        if (startRun == null) return false;

        // 如果匹配完全在一个Run内，则不是跨Run匹配
        if (endIndex <= startRun.EndPosition) return false;

        return true; // 跨越多个Run
    }

    /// <summary>
    /// 处理单个跨Run匹配
    /// </summary>
    private void ProcessSingleCrossRunMatch(FunctionMatch match, List<RunMapping> runMappings)
    {
        // 找到涉及的Run范围
        var affectedRuns = runMappings
            .Where(rm => rm.StartPosition < match.StartIndex + match.Length && rm.EndPosition > match.StartIndex)
            .OrderBy(rm => rm.StartPosition)
            .ToList();

        if (affectedRuns.Count == 0) return;

        // 对每个受影响的Run进行处理
        foreach (var runMapping in affectedRuns)
        {
            // 计算函数在当前Run中的相对位置
            int functionStartInRun = Math.Max(0, match.StartIndex - runMapping.StartPosition);
            int functionEndInRun = Math.Min(runMapping.Run.Text?.Length ?? 0,
                                          match.StartIndex + match.Length - runMapping.StartPosition);

            // 确保位置有效
            if (functionStartInRun < functionEndInRun && functionEndInRun <= (runMapping.Run.Text?.Length ?? 0))
            {
                SplitRunForFunction(runMapping.Run, functionStartInRun, functionEndInRun);
            }
        }
    }

    /// <summary>
    /// 一次性处理一个Run中的多个函数匹配
    /// 关键修复：避免分割过程中文本变化影响后续匹配
    /// </summary>
    /// <param name="run">要处理的Run</param>
    /// <param name="matches">所有函数匹配</param>
    /// <param name="originalText">原始文本</param>
    private void SplitRunForMultipleFunctions(Run run, List<FunctionMatch> matches, string originalText)
    {


        // 按位置排序，确保处理顺序正确
        matches = matches.OrderBy(m => m.StartIndex).ToList();

        // 去除重叠匹配，保留最长匹配
        var nonOverlappingMatches = RemoveOverlappingMatches(matches);



        // 构建所有文本段
        var segments = new List<(string text, bool isFunction)>();
        int currentPosition = 0;

        foreach (var match in nonOverlappingMatches)
        {
            // 添加函数前的文本（如果有）
            if (match.StartIndex > currentPosition)
            {
                string beforeText = originalText.Substring(currentPosition, match.StartIndex - currentPosition);
                if (!string.IsNullOrEmpty(beforeText))
                {
                    segments.Add((beforeText, false));
                }
            }

            // 添加函数文本
            string functionText = originalText.Substring(match.StartIndex, match.Length);
            if (!string.IsNullOrEmpty(functionText))
            {
                segments.Add((functionText, true));


            }

            currentPosition = match.StartIndex + match.Length;
        }

        // 添加最后剩余的文本（如果有）
        if (currentPosition < originalText.Length)
        {
            string remainingText = originalText.Substring(currentPosition);
            if (!string.IsNullOrEmpty(remainingText))
            {
                segments.Add((remainingText, false));
            }
        }



        // 如果没有段，直接返回
        if (segments.Count == 0) return;

        // 使用现有的分割逻辑处理所有段
        ApplySegmentsToRun(run, segments);
    }

    /// <summary>
    /// 将文本段应用到Run
    /// </summary>
    private void ApplySegmentsToRun(Run run, List<(string text, bool isFunction)> segments)
    {
        if (segments.Count == 0) return;
        // Console.WriteLine($"DEBUG: {run.Text}={run.Font.Bold}");

        // 关键：在任何修改之前先克隆原始Run，保存原始状态
        Run originalRun = (Run)run.Clone(true);

        // 如果只有一个段，直接设置格式
        if (segments.Count == 1)
        {
            run.Text = segments[0].text;
            if (segments[0].isFunction)
            {
                run.Font.Italic = false; // 函数设置为非斜体
            }
            return;
        }

        // 将原Run用于第一个文本段
        run.Text = segments[0].text;
        if (segments[0].isFunction)
        {
            run.Font.Italic = false; // 函数设置为非斜体
        }



        // 跟踪最后插入的节点
        Run lastInsertedRun = run;

        // 处理后续的文本段
        for (int i = 1; i < segments.Count; i++)
        {
            // 克隆原始Run，保持完全一致的格式
            Run newRun = (Run)originalRun.Clone(true);
            newRun.Text = segments[i].text;
            // Console.WriteLine($"DEBUG: {newRun.Text}={originalRun.Font.Bold}");

            if (segments[i].isFunction)
            {
                newRun.Font.Italic = false; // 函数设置为非斜体
            }

            // 强制修复：特别处理复杂段落中的函数
            if (segments[i].isFunction)
            {
                // 关键修复：强制重置所有格式属性
                newRun.Font.Italic = false;
                // Console.WriteLine($"DEBUG: {newRun.Text}={newRun.Font.Bold}"); 
                // Console.WriteLine($"DEBUG: newRun.Font.italic={newRun.Font.Italic}"); 
                // newRun.Font.Bold = false;

                // 强制刷新字体设置
                // if (newRun.Font.Name != null)
                // {
                //     string currentFont = newRun.Font.Name;
                //     newRun.Font.Name = currentFont; // 强制刷新
                // }
            }

            // 在最后插入的节点后插入新节点
            run.ParentNode.InsertAfter(newRun, lastInsertedRun);
            lastInsertedRun = newRun;

            // 关键修复：插入后立即验证和强制设置所有函数格式
            if (segments[i].isFunction)
            {
                // 强制设置格式，确保生效
                newRun.Font.Italic = false;
                // newRun.Font.Bold = false;
            }
        }

        // 最终修复：在所有处理完成后，扫描并强制修复所有函数
        if (segments.Any(s => s.isFunction))
        {
            // 获取当前段落中的所有Run
            if (run.GetAncestor(NodeType.Paragraph) is Paragraph paragraph)
            {
                var allRuns = paragraph.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();

                foreach (var currentRun in allRuns)
                {
                    // 检查当前Run的文本是否是目标函数
                    string runText = currentRun.Text ?? "";
                    if (_targetFunctions.Contains(runText))
                    {
                        // 强制设置为非斜体
                        currentRun.Font.Italic = false;
                        // currentRun.Font.Bold = false;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 在单个文本中查找所有函数匹配
    /// </summary>
    /// <param name="text">要搜索的文本</param>
    /// <returns>所有匹配的函数列表</returns>
    private List<FunctionMatch> FindFunctionMatchesInText(string text)
    {
        var matches = new List<FunctionMatch>();



        foreach (string targetFunction in _targetFunctions)
        {
            int searchStart = 0;
            while (true)
            {
                int foundIndex = text.IndexOf(targetFunction, searchStart, StringComparison.Ordinal);
                if (foundIndex == -1) break;

                var match = new FunctionMatch
                {
                    FunctionName = targetFunction,
                    StartIndex = foundIndex,
                    Length = targetFunction.Length
                };

                matches.Add(match);



                searchStart = foundIndex + 1; // 继续搜索重叠匹配
            }
        }

        var sortedMatches = matches.OrderBy(m => m.StartIndex).ThenByDescending(m => m.Length).ToList();



        return sortedMatches;
    }

    /// <summary>
    /// 构建Run映射关系
    /// </summary>
    private List<RunMapping> BuildRunMappings(List<Run> runs)
    {
        var runMappings = new List<RunMapping>();
        int currentPosition = 0;

        foreach (var run in runs)
        {
            string runText = run.Text ?? "";
            runMappings.Add(new RunMapping
            {
                Run = run,
                StartPosition = currentPosition,
                EndPosition = currentPosition + runText.Length,
                Text = runText
            });
            currentPosition += runText.Length;
        }

        return runMappings;
    }

    /// <summary>
    /// 查找第一个函数匹配（按照用户思路：从前到后）
    /// </summary>
    /// <param name="text">要搜索的文本</param>
    /// <returns>第一个匹配的函数，如果没有则返回null</returns>
    private FunctionMatch? FindFirstFunctionMatch(string text)
    {
        FunctionMatch? earliestMatch = null;
        int earliestPosition = int.MaxValue;

        foreach (string targetFunction in _targetFunctions)
        {
            int index = text.IndexOf(targetFunction, StringComparison.Ordinal);

            if (index >= 0 && index < earliestPosition)
            {
                earliestPosition = index;
                earliestMatch = new FunctionMatch
                {
                    FunctionName = targetFunction,
                    StartIndex = index,
                    Length = targetFunction.Length
                };
            }
        }

        return earliestMatch;
    }

    /// <summary>
    /// 处理单个函数匹配
    /// </summary>
    /// <param name="match">函数匹配信息</param>
    /// <param name="runMappings">Run映射信息</param>
    private void ProcessSingleMatch(FunctionMatch match, List<RunMapping> runMappings)
    {
        // 找到涉及的Run范围
        var affectedRuns = runMappings
            .Where(rm => rm.StartPosition < match.StartIndex + match.Length && rm.EndPosition > match.StartIndex)
            .OrderBy(rm => rm.StartPosition)
            .ToList();

        if (affectedRuns.Count == 0) return;

        // 对每个受影响的Run进行处理
        foreach (var runMapping in affectedRuns)
        {
            // 计算函数在当前Run中的相对位置
            int functionStartInRun = Math.Max(0, match.StartIndex - runMapping.StartPosition);
            int functionEndInRun = Math.Min(runMapping.Run.Text?.Length ?? 0,
                                          match.StartIndex + match.Length - runMapping.StartPosition);

            // 确保位置有效
            if (functionStartInRun < functionEndInRun && functionEndInRun <= (runMapping.Run.Text?.Length ?? 0))
            {
                SplitRunForFunction(runMapping.Run, functionStartInRun, functionEndInRun);
            }
        }
    }

    /// <summary>
    /// 查找全文中所有的函数匹配（支持部分匹配）
    /// </summary>
    /// <param name="fullText">完整的连续文本</param>
    /// <returns>所有匹配的函数列表</returns>
    private List<FunctionMatch> FindAllFunctionMatches(string fullText)
    {
        var matches = new List<FunctionMatch>();

        foreach (string targetFunction in _targetFunctions)
        {
            int searchStart = 0;
            while (true)
            {
                int foundIndex = fullText.IndexOf(targetFunction, searchStart, StringComparison.Ordinal);
                if (foundIndex == -1) break;

                // 按照用户设想：只要能匹配上就算匹配，不需要单词边界检查
                // 支持部分匹配，如asinnx中的sin
                matches.Add(new FunctionMatch
                {
                    StartIndex = foundIndex,
                    Length = targetFunction.Length,
                    FunctionName = targetFunction
                });

                searchStart = foundIndex + 1;
            }
        }

        return matches;
    }

    /// <summary>
    /// 处理跨Run的函数匹配
    /// </summary>
    /// <param name="match">函数匹配信息</param>
    /// <param name="runMappings">Run映射列表</param>
    private void ProcessCrossRunMatch(FunctionMatch match, List<RunMapping> runMappings)
    {
        int functionStart = match.StartIndex;
        int functionEnd = match.StartIndex + match.Length;

        // 找到所有受影响的Run
        var affectedRuns = runMappings.Where(rm =>
            rm.StartPosition < functionEnd && rm.EndPosition > functionStart).ToList();

        if (affectedRuns.Count == 0) return;

        // 按照用户设想：不合并Run，分别处理每个受影响的Run
        foreach (var runMapping in affectedRuns)
        {
            // 计算函数在全局文本中的位置范围
            int globalFunctionStart = functionStart;
            int globalFunctionEnd = functionEnd;

            // 计算当前Run的全局位置范围
            int runGlobalStart = runMapping.StartPosition;
            int runGlobalEnd = runMapping.EndPosition;

            // 计算函数与当前Run的交集部分在Run内的相对位置
            int runLocalStart = Math.Max(0, globalFunctionStart - runGlobalStart);
            int runLocalEnd = Math.Min(runMapping.Text.Length, globalFunctionEnd - runGlobalStart);

            // 只有当交集部分有效时才进行分割
            if (runLocalStart < runLocalEnd && runLocalStart >= 0 && runLocalEnd <= runMapping.Text.Length)
            {
                // 关键修复：只分割函数在当前Run中的部分
                SplitRunForFunction(runMapping.Run, runLocalStart, runLocalEnd);
            }
        }
    }





    /// <summary>
    /// 去除重叠的匹配，保留最长的匹配
    /// </summary>
    /// <param name="matches">所有匹配列表</param>
    /// <returns>去除重叠后的匹配列表</returns>
    private List<FunctionMatch> RemoveOverlappingMatches(List<FunctionMatch> matches)
    {
        if (matches.Count <= 1) return matches;



        // 按开始位置排序
        matches.Sort((a, b) => a.StartIndex.CompareTo(b.StartIndex));

        var result = new List<FunctionMatch>();

        foreach (var match in matches)
        {
            int matchEnd = match.StartIndex + match.Length;
            bool hasOverlap = false;

            // 检查是否与已选择的匹配重叠
            foreach (var selected in result)
            {
                int selectedEnd = selected.StartIndex + selected.Length;

                // 检查重叠：当前匹配的开始位置在已选择匹配的范围内，或者已选择匹配的开始位置在当前匹配的范围内
                if ((match.StartIndex < selectedEnd && matchEnd > selected.StartIndex))
                {
                    hasOverlap = true;

                    // 如果当前匹配更长，替换已选择的匹配
                    if (match.Length > selected.Length)
                    {
                        result.Remove(selected);
                        result.Add(match);
                    }
                    break;
                }
            }

            // 如果没有重叠，直接添加
            if (!hasOverlap)
            {
                result.Add(match);
            }
        }

        return result;
    }



    /// <summary>
    /// 分割Run节点以精确设置函数部分的格式
    /// </summary>
    /// <param name="run">要分割的Run节点</param>
    /// <param name="functionStart">函数在Run中的开始位置</param>
    /// <param name="functionEnd">函数在Run中的结束位置</param>
    /// <remarks>
    /// 按照用户的清晰思路实现：
    /// 1. 将原Run克隆成多份，每份格式都与原本完全一致
    /// 2. 逐一设置每份的文本内容
    /// 3. 只有函数部分设置为非斜体，其他部分格式不变
    /// 4. 避免复杂的格式继承问题
    /// </remarks>
    private void SplitRunForFunction(Run run, int functionStart, int functionEnd)
    {
        string originalText = run.Text ?? "";
        if (string.IsNullOrEmpty(originalText)) return;

        // 验证索引范围
        if (functionStart < 0 || functionEnd > originalText.Length || functionStart >= functionEnd)
        {
            return;
        }







        // 关键：在任何修改之前先克隆原始Run，保存原始状态
        Run originalRun = (Run)run.Clone(true);

        // 分割文本为三部分
        string beforeFunction = functionStart > 0 ? originalText.Substring(0, functionStart) : "";
        string functionText = originalText.Substring(functionStart, functionEnd - functionStart);
        string afterFunction = functionEnd < originalText.Length ? originalText.Substring(functionEnd) : "";

        // 创建文本段列表
        var segments = new List<(string text, bool isFunction)>();



        if (!string.IsNullOrEmpty(beforeFunction))
            segments.Add((beforeFunction, false));

        if (!string.IsNullOrEmpty(functionText))
            segments.Add((functionText, true));

        if (!string.IsNullOrEmpty(afterFunction))
            segments.Add((afterFunction, false));

        // 调试：显示段信息
        bool hasMinSegment = segments.Any(s => s.text == "min");
        if (hasMinSegment)
        {
            for (int i = 0; i < segments.Count; i++)
            {
            }
        }

        // 强制调试：显示所有段的处理过程
        if (segments.Count > 2)
        {
            for (int i = 0; i < segments.Count; i++)
            {
            }
        }

        // 特别调试：显示包含min的多段处理
        if (segments.Count > 2 && segments.Any(s => s.text == "min"))
        {
            for (int i = 0; i < segments.Count; i++)
            {
            }
        }

        // 强制调试：显示6段处理的详细信息
        if (segments.Count == 6)
        {
            for (int i = 0; i < segments.Count; i++)
            {
            }
        }

        // 如果只有一个段，直接设置格式
        if (segments.Count == 1)
        {
            if (segments[0].isFunction)
            {
                if (segments[0].text == "min")
                {
                }
                run.Font.Italic = false; // 函数设置为非斜体
                if (segments[0].text == "min")
                {
                }
            }
            else
            {
                if (segments[0].text == "min")
                {
                }
            }
            // 非函数部分保持原有格式
            return;
        }

        // 按照用户的清晰思路：将原Run用于第一个文本段
        run.Text = segments[0].text;
        if (segments[0].isFunction)
        {
            if (segments[0].text == "min")
            {
            }
            run.Font.Italic = false; // 函数设置为非斜体
            if (segments[0].text == "min")
            {
            }
        }
        else
        {
            if (segments[0].text == "min")
            {
            }
        }

        // 强制调试：显示第一段处理
        if (segments.Count > 2)
        {
        }

        // 特别调试：min函数第一段处理
        if (segments[0].text == "min")
        {
        }

        // 强制调试：6段处理的第一段
        if (segments.Count == 6)
        {
        }

        // 非函数部分保持原有格式（不修改）



        // 跟踪最后插入的节点
        Run lastInsertedRun = run;

        // 遍历处理后续的文本段，从索引1开始
        for (int i = 1; i < segments.Count; i++)
        {
            // 关键：克隆原始Run（修改前的状态），保持完全一致的格式
            Run newRun = (Run)originalRun.Clone(true);
            newRun.Text = segments[i].text;

            if (segments[i].isFunction)
            {
                if (segments[i].text == "min")
                {
                }
                newRun.Font.Italic = false; // 函数设置为非斜体
                if (segments[i].text == "min")
                {
                }
            }
            else
            {
                if (segments[i].text == "min")
                {
                }
            }

            // 强制调试：显示每个段的处理
            if (segments.Count > 2)
            {
            }

            // 特别调试：min函数后续段处理
            if (segments[i].text == "min")
            {

                // 验证插入后的Run状态
            }

            // 强制调试：6段处理的后续段
            if (segments.Count == 6)
            {

                // 特别关注第5段（min函数）
                if (i == 5 && segments[i].text == "min")
                {

                    // 强制设置为非斜体
                    if (segments[i].isFunction)
                    {
                        newRun.Font.Italic = false;
                    }
                }
            }

            // 非函数部分保持原始格式（不修改）



            // 在最后插入的节点后插入新节点
            run.ParentNode.InsertAfter(newRun, lastInsertedRun);

            // 调试：验证插入后的Run状态
            if (segments[i].text == "min")
            {

                // 强制刷新格式设置
                if (segments[i].isFunction)
                {
                    newRun.Font.Italic = false;
                }
            }

            // 更新最后插入的节点
            lastInsertedRun = newRun;
        }
    }

    /// <summary>
    /// 内部类：函数匹配信息
    /// </summary>
    /// <remarks>
    /// 用于记录在文本中找到的函数匹配信息
    /// </remarks>
    private class FunctionMatch
    {
        public int StartIndex { get; set; }         // 函数在文本中的开始位置
        public int Length { get; set; }             // 函数长度
        public string FunctionName { get; set; } = string.Empty; // 函数名
    }

    /// <summary>
    /// 内部类：Run映射信息
    /// </summary>
    /// <remarks>
    /// 用于记录Run节点在连续文本中的位置映射关系
    /// </remarks>
    private class RunMapping
    {
        public Run Run { get; set; } = null!;       // Run节点引用
        public int StartPosition { get; set; }      // 在连续文本中的开始位置
        public int EndPosition { get; set; }        // 在连续文本中的结束位置
        public string Text { get; set; } = string.Empty; // Run的文本内容
    }
}