using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Tables;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// 标准化表格处理步骤（实现IPipelineStep接口）
// 功能：对文档中的表格进行标准化处理，包括格式设置和宽度调整
public class NormalizeTableStep : IPipelineStep
{
    private readonly string _grayMode;
    private readonly int _leftAlignThreshold;

    // 构造函数：初始化灰色底纹模式和居左对齐阈值
    // grayMode：灰色底纹模式。"gray.on"为启用答案区域表格灰色底纹；"gray.off"为关闭灰色底纹
    // leftAlignThreshold：字符数阈值，单元格字符数大于等于此值时，该单元格及其列的其他单元格（除第一行）居左对齐
    public NormalizeTableStep(string grayMode = "gray.off", int leftAlignThreshold = 24)
    {
        _grayMode = grayMode ?? "gray.off";
        _leftAlignThreshold = leftAlignThreshold;
    }
    // 主执行方法：文档处理入口
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中的所有表格
            NodeCollection tables = doc.GetChildNodes(NodeType.Table, true);
            
            if (tables.Count == 0)
            {
                return true;
            }

            // 遍历处理每个表格
            foreach (Table table in tables.OfType<Table>())
            {
                ProcessTable(table);
            }

            // 如果启用灰色底纹模式，处理答案区域表格
            if (_grayMode == "gray.on")
            {
                ProcessAnswerAreaTables(doc);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 处理单个表格
    private void ProcessTable(Table table)
    {
        // 1. 分析需要居左对齐的列（只针对字符数超过阈值的情况）
        var leftAlignColumns = AnalyzeCharCountLeftAlignColumns(table);
        
        // 2. 应用基础表格格式（传入居左对齐列信息）
        ApplyBasicTableFormat(table, leftAlignColumns);
        
        // 3. 根据列数和内容设置宽度
        SetTableWidthByColumns(table);
    }

    // 分析需要居左对齐的列（只针对字符数超过阈值的情况）
    private HashSet<int> AnalyzeCharCountLeftAlignColumns(Table table)
    {
        var leftAlignColumns = new HashSet<int>();
        
        if (table.Rows.Count == 0) return leftAlignColumns;
        
        // 遍历所有单元格，检查字符数（不包括下划线检查）
        for (int rowIndex = 0; rowIndex < table.Rows.Count; rowIndex++)
        {
            Row row = table.Rows[rowIndex];
            for (int cellIndex = 0; cellIndex < row.Cells.Count; cellIndex++)
            {
                Cell cell = row.Cells[cellIndex];
                
                // 只检查字符数，不检查下划线
                int charCount = CalculateCharacterCount(cell.GetText());
                
                // 如果单元格字符数大于等于阈值，该列需要居左对齐
                if (charCount >= _leftAlignThreshold)
                {
                    leftAlignColumns.Add(cellIndex);
                }
            }
        }
        
        return leftAlignColumns;
    }

    // 检查单元格是否包含下划线
    private bool CellHasUnderline(Cell cell)
    {
        try
        {
            // 获取单元格中的所有Run节点
            NodeCollection runs = cell.GetChildNodes(NodeType.Run, true);
            
            foreach (Run run in runs.OfType<Run>())
            {
                // 检查Run是否有下划线格式（参考NormalizeUnderlineStep.cs的逻辑）
                if (run.Font.Underline != Underline.None)
                {
                    return true;
                }
            }
            
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 应用基础表格格式
    private void ApplyBasicTableFormat(Table table, HashSet<int> leftAlignColumns)
    {
        // 设置表格左缩进为0.21厘米
        table.LeftIndent = ConvertCmToPoints(0.21);
        
        // 设置表格整体对齐方式为居左
        table.Alignment = TableAlignment.Left;
        
        // 处理所有单元格
        foreach (Row row in table.Rows)
        {
            foreach (Cell cell in row.Cells)
            {
                // *** 关键修复：设置单元格垂直居中对齐 ***
                cell.CellFormat.VerticalAlignment = CellVerticalAlignment.Center;
                
                // 处理单元格内的所有段落
                foreach (Paragraph paragraph in cell.Paragraphs)
                {
                    // *** 新增：判断段落对齐方式 ***
                    ParagraphAlignment alignment = DetermineParagraphAlignment(table, row, cell, leftAlignColumns);
                    paragraph.ParagraphFormat.Alignment = alignment;
                    
                    // 设置段落左缩进为0字符
                    paragraph.ParagraphFormat.LeftIndent = 0;
                    
                    // 设置段落特殊格式为无（取消首行缩进等）
                    paragraph.ParagraphFormat.FirstLineIndent = 0;
                    
                    // *** 新增：设置段落垂直间距为0以确保垂直居中 ***
                    paragraph.ParagraphFormat.SpaceBefore = 0;
                    paragraph.ParagraphFormat.SpaceAfter = 0;
                    paragraph.ParagraphFormat.LineSpacing = 0;
                    paragraph.ParagraphFormat.LineSpacingRule = LineSpacingRule.AtLeast;
                    
                    // 特殊处理包含OMML公式的段落
                    if (alignment == ParagraphAlignment.Center)
                    {
                        ProcessOmmlParagraphAlignment(paragraph, cell);
                    }
                    else if (alignment == ParagraphAlignment.Left)
                    {
                        ProcessOmmlParagraphLeftAlignment(paragraph, cell);
                    }
                }
            }
        }
    }
    
    // 判断段落的对齐方式
    private ParagraphAlignment DetermineParagraphAlignment(Table table, Row row, Cell cell, HashSet<int> leftAlignColumns)
    {
        try
        {
            // 获取当前单元格的列索引
            int cellIndex = GetCellColumnIndex(row, cell);
            
            // 获取当前行的索引
            int rowIndex = GetRowIndex(table, row);
            
            // 情况1：如果当前单元格包含下划线，该单元格居左（不影响其他单元格）
            if (CellHasUnderline(cell))
            {
                return ParagraphAlignment.Left;
            }
            
            // 情况2：如果当前单元格字符数大于等于阈值，该单元格居左
            int charCount = CalculateCharacterCount(cell.GetText());
            if (charCount >= _leftAlignThreshold)
            {
                return ParagraphAlignment.Left;
            }
            
            // 情况3：如果当前单元格所在的列需要居左对齐（由于字符数超过阈值），且不是第一行，则居左
            if (leftAlignColumns.Contains(cellIndex) && rowIndex > 0)
            {
                return ParagraphAlignment.Left;
            }
            
            // 其他情况：居中对齐（保持原有逻辑）
            return ParagraphAlignment.Center;
        }
        catch (Exception)
        {
            // 异常情况下保持原有逻辑：居中对齐
            return ParagraphAlignment.Center;
        }
    }
    
    // 获取单元格在行中的列索引
    private int GetCellColumnIndex(Row row, Cell targetCell)
    {
        try
        {
            for (int i = 0; i < row.Cells.Count; i++)
            {
                if (row.Cells[i] == targetCell)
                {
                    return i;
                }
            }
            return 0;
        }
        catch (Exception)
        {
            return 0;
        }
    }
    
    // 获取行在表格中的索引
    private int GetRowIndex(Table table, Row targetRow)
    {
        try
        {
            for (int i = 0; i < table.Rows.Count; i++)
            {
                if (table.Rows[i] == targetRow)
                {
                    return i;
                }
            }
            return 0;
        }
        catch (Exception)
        {
            return 0;
        }
    }

    // 根据列数和内容设置表格宽度
    private void SetTableWidthByColumns(Table table)
    {
        if (table.Rows.Count == 0) return;
        
        int columnCount = table.Rows[0].Cells.Count;
        
        // 计算每列的最大字符数
        int[] maxCharsPerColumn = CalculateMaxCharsPerColumn(table);
        int overallMaxChars = maxCharsPerColumn.Max();
        
        switch (columnCount)
        {
            case 1:
                HandleOneColumnTable(table, overallMaxChars);
                break;
            case 2:
                HandleTwoColumnTable(table, overallMaxChars);
                break;
            case 3:
                HandleThreeColumnTable(table, maxCharsPerColumn);
                break;
            case 4:
                HandleFourColumnTable(table, maxCharsPerColumn);
                break;
            case 5:
                HandleFiveColumnTable(table, maxCharsPerColumn);
                break;
            default:
                HandleMoreThanFiveColumnTable(table);
                break;
        }
    }

    // 计算每列的最大字符数
    private int[] CalculateMaxCharsPerColumn(Table table)
    {
        if (table.Rows.Count == 0) return new int[0];
        
        int columnCount = table.Rows[0].Cells.Count;
        int[] maxCharsPerColumn = new int[columnCount];
        
        foreach (Row row in table.Rows)
        {
            for (int i = 0; i < Math.Min(row.Cells.Count, columnCount); i++)
            {
                Cell cell = row.Cells[i];
                int charCount = CalculateCharacterCount(cell.GetText());
                maxCharsPerColumn[i] = Math.Max(maxCharsPerColumn[i], charCount);
            }
        }
        
        return maxCharsPerColumn;
    }

    // 计算字符数（中文汉字和标点符号为2字符，其他为1字符）
    private int CalculateCharacterCount(string text)
    {
        if (string.IsNullOrEmpty(text)) return 0;
        
        int charCount = 0;
        
        foreach (char c in text)
        {
            // 跳过控制字符（如换行符、制表符等）
            if (char.IsControl(c)) continue;
            
            // 判断是否为中文字符或中文标点符号
            if (IsCjkCharacter(c))
            {
                charCount += 2; // 中文字符计为2个字符
            }
            else
            {
                charCount += 1; // 其他字符计为1个字符
            }
        }
        
        return charCount;
    }

    // 判断是否为中日韩字符（包括汉字和中文标点符号）
    private bool IsCjkCharacter(char c)
    {
        // 中日韩统一表意文字 (CJK Unified Ideographs)
        if (c >= 0x4E00 && c <= 0x9FFF) return true;
        
        // 中日韩统一表意文字扩展A (CJK Unified Ideographs Extension A)
        if (c >= 0x3400 && c <= 0x4DBF) return true;
        
        // 中日韩兼容表意文字 (CJK Compatibility Ideographs)
        if (c >= 0xF900 && c <= 0xFAFF) return true;
        
        // 中日韩符号和标点 (CJK Symbols and Punctuation)
        if (c >= 0x3000 && c <= 0x303F) return true;
        
        // 中日韩兼容符号 (CJK Compatibility)
        if (c >= 0x3300 && c <= 0x33FF) return true;
        
        // 全角ASCII、全角标点符号 (Fullwidth ASCII variants)
        if (c >= 0xFF00 && c <= 0xFFEF) return true;
        
        // 半角和全角形式 (Halfwidth and Fullwidth Forms)
        if (c >= 0xFF01 && c <= 0xFF60) return true;
        
        // 一些常见的中文标点符号
        string chinesePunctuation = "，。？！；：\"\"''（）【】《》〈〉『』「」〔〕｛｝〖〗…—–";
        if (chinesePunctuation.Contains(c)) return true;
        
        return false;
    }

    // 处理一列表格
    private void HandleOneColumnTable(Table table, int maxChars)
    {
        // 取消表格整体指定宽度
        table.PreferredWidth = PreferredWidth.Auto;
        
        double columnWidth;
        if (maxChars <= 14)
            columnWidth = 3.0;
        else if (maxChars <= 18)
            columnWidth = 4.0;
        else if (maxChars <= 23)
            columnWidth = 5.0;
        else if (maxChars <= 29)
            columnWidth = 6.0;
        else if (maxChars <= 34)
            columnWidth = 6.0;
        else
            columnWidth = 7.0;
        
        SetColumnWidth(table, 0, columnWidth);
    }

    // 处理两列表格
    private void HandleTwoColumnTable(Table table, int maxChars)
    {
        // 取消表格整体指定宽度
        table.PreferredWidth = PreferredWidth.Auto;
        
        double columnWidth;
        if (maxChars <= 14)
            columnWidth = 3.0;
        else if (maxChars <= 18)
            columnWidth = 4.0;
        else if (maxChars <= 23)
            columnWidth = 5.0;
        else if (maxChars <= 29)
            columnWidth = 6.0;
        else
            columnWidth = 7.0;
        
        SetColumnWidth(table, 0, columnWidth);
        SetColumnWidth(table, 1, columnWidth);
    }

    // 处理三列表格
    private void HandleThreeColumnTable(Table table, int[] maxCharsPerColumn)
    {
        // 取消表格整体指定宽度
        table.PreferredWidth = PreferredWidth.Auto;
        
        int maxChars = maxCharsPerColumn.Max();
        
        if (maxChars <= 14)
        {
            SetColumnWidth(table, 0, 3.0);
            SetColumnWidth(table, 1, 3.0);
            SetColumnWidth(table, 2, 3.0);
        }
        else if (maxChars <= 18)
        {
            SetColumnWidth(table, 0, 4.0);
            SetColumnWidth(table, 1, 4.0);
            SetColumnWidth(table, 2, 4.0);
        }
        else
        {
            // maxChars > 18 的情况下按照第一列与后两列的关系设置
            int firstColumnMax = maxCharsPerColumn[0];
            int lastTwoColumnsMax = Math.Max(maxCharsPerColumn[1], maxCharsPerColumn[2]);
            
            if (firstColumnMax > lastTwoColumnsMax)
            {
                SetColumnWidth(table, 0, 6.0);
                SetColumnWidth(table, 1, 4.0);
                SetColumnWidth(table, 2, 4.0);
            }
            else if (firstColumnMax < lastTwoColumnsMax)
            {
                SetColumnWidth(table, 0, 4.0);
                SetColumnWidth(table, 1, 5.0);
                SetColumnWidth(table, 2, 5.0);
            }
            else // firstColumnMax == lastTwoColumnsMax
            {
                SetColumnWidth(table, 0, 4.8);
                SetColumnWidth(table, 1, 4.6);
                SetColumnWidth(table, 2, 4.6);
            }
        }
    }

    // 处理四列表格
    private void HandleFourColumnTable(Table table, int[] maxCharsPerColumn)
    {
        // 取消表格整体指定宽度
        table.PreferredWidth = PreferredWidth.Auto;
        
        int maxChars = maxCharsPerColumn.Max();
        
        if (maxChars <= 10)
        {
            for (int i = 0; i < 4; i++)
            {
                SetColumnWidth(table, i, 2.5);
            }
        }
        else if (maxChars <= 14)
        {
            for (int i = 0; i < 4; i++)
            {
                SetColumnWidth(table, i, 3.0);
            }
        }
        else
        {
            int firstColumnMax = maxCharsPerColumn[0];
            int lastThreeColumnsMax = Math.Max(Math.Max(maxCharsPerColumn[1], maxCharsPerColumn[2]), maxCharsPerColumn[3]);
            
            if (firstColumnMax > 14 && lastThreeColumnsMax <= 14)
            {
                SetColumnWidth(table, 0, 5.0);
                SetColumnWidth(table, 1, 3.0);
                SetColumnWidth(table, 2, 3.0);
                SetColumnWidth(table, 3, 3.0);
            }
            else if (firstColumnMax <= 14 && lastThreeColumnsMax > 14)
            {
                SetColumnWidth(table, 0, 3.2);
                SetColumnWidth(table, 1, 3.6);
                SetColumnWidth(table, 2, 3.6);
                SetColumnWidth(table, 3, 3.6);
            }
            else
            {
                for (int i = 0; i < 4; i++)
                {
                    SetColumnWidth(table, i, 3.5);
                }
            }
        }
    }

    // 处理五列表格
    private void HandleFiveColumnTable(Table table, int[] maxCharsPerColumn)
    {
        // 取消表格整体指定宽度
        table.PreferredWidth = PreferredWidth.Auto;
        
        int maxChars = maxCharsPerColumn.Max();
        
        if (maxChars <= 2)
        {
            for (int i = 0; i < 5; i++)
            {
                SetColumnWidth(table, i, 1.0);
            }
        }
        else if (maxChars <= 10)
        {
            for (int i = 0; i < 5; i++)
            {
                SetColumnWidth(table, i, 2.5);
            }
        }
        else
        {
            int firstColumnMax = maxCharsPerColumn[0];
            int lastFourColumnsMax = Math.Max(Math.Max(Math.Max(maxCharsPerColumn[1], maxCharsPerColumn[2]), maxCharsPerColumn[3]), maxCharsPerColumn[4]);
            
            if (firstColumnMax > 10 && lastFourColumnsMax <= 10)
            {
                SetColumnWidth(table, 0, 4.0);
                SetColumnWidth(table, 1, 2.5);
                SetColumnWidth(table, 2, 2.5);
                SetColumnWidth(table, 3, 2.5);
                SetColumnWidth(table, 4, 2.5);
            }
            else if (firstColumnMax <= 10 && lastFourColumnsMax > 10)
            {
                SetColumnWidth(table, 0, 2.0);
                SetColumnWidth(table, 1, 3.0);
                SetColumnWidth(table, 2, 3.0);
                SetColumnWidth(table, 3, 3.0);
                SetColumnWidth(table, 4, 3.0);
            }
            else
            {
                for (int i = 0; i < 5; i++)
                {
                    SetColumnWidth(table, i, 2.8);
                }
            }
        }
    }

    // 处理超过五列的表格
    private void HandleMoreThanFiveColumnTable(Table table)
    {
        // 设置表格整体指定宽度为14厘米
        table.PreferredWidth = PreferredWidth.FromPoints(ConvertCmToPoints(14.0));
        
        // 每列宽度一致
        if (table.Rows.Count > 0)
        {
            int columnCount = table.Rows[0].Cells.Count;
            double columnWidth = 14.0 / columnCount;
            
            for (int i = 0; i < columnCount; i++)
            {
                SetColumnWidth(table, i, columnWidth);
            }
        }
    }

    // 设置指定列的宽度
    private void SetColumnWidth(Table table, int columnIndex, double widthInCm)
    {
        double widthInPoints = ConvertCmToPoints(widthInCm);
        
        foreach (Row row in table.Rows)
        {
            if (columnIndex < row.Cells.Count)
            {
                Cell cell = row.Cells[columnIndex];
                cell.CellFormat.PreferredWidth = PreferredWidth.FromPoints(widthInPoints);
            }
        }
    }

    // 将厘米转换为点数（Aspose.Words使用点数作为单位）
    private double ConvertCmToPoints(double cm)
    {
        // 1厘米 = 28.3465点
        return cm * 28.3465;
    }

    // 特殊处理包含OMML公式的段落对齐问题
    private void ProcessOmmlParagraphAlignment(Paragraph paragraph, Cell cell)
    {
        try
        {
            // 检查段落是否包含OMML公式
            if (!ContainsOmmlFormula(paragraph)) return;
            
            if (IsPureOmmlParagraph(paragraph))
            {
                // 纯OMML段落：完整的特殊处理，确保正确对齐
                HandleOmmlParagraphAlignment(paragraph, cell);
            }
            // 普通段落（包括混合内容段落）：完全不处理，使用正常的段落对齐
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    // 检查段落是否包含OMML公式
    private bool ContainsOmmlFormula(Paragraph paragraph)
    {
        // 获取段落中的所有OMML公式节点
        NodeCollection ommlNodes = paragraph.GetChildNodes(NodeType.OfficeMath, true);
        return ommlNodes.Count > 0;
    }

    // 检查是否是纯OMML公式段落（所有字符都在OMML公式内，公式外无任何字符）
    private bool IsPureOmmlParagraph(Paragraph paragraph)
    {
        // 获取所有OMML公式节点
        NodeCollection ommlNodes = paragraph.GetChildNodes(NodeType.OfficeMath, true);
        
        if (ommlNodes.Count == 0) return false;
        
        // 获取段落的所有子节点
        NodeCollection allNodes = paragraph.GetChildNodes(NodeType.Any, false);
        
        // 检查段落中是否只有OMML公式节点
        // 纯OMML段落应该只包含OMML公式节点，不包含其他文本节点
        foreach (Node node in allNodes)
        {
            if (node.NodeType == NodeType.OfficeMath)
            {
                // OMML公式节点，继续检查
                continue;
            }
            else if (node.NodeType == NodeType.Run)
            {
                // 检查Run节点是否只包含空白字符
                Run run = (Run)node;
                string runText = run.Text.Trim();
                if (!string.IsNullOrEmpty(runText))
                {
                    // 有非空白文本，不是纯OMML段落
                    return false;
                }
            }
            else
            {
                // 有其他类型的节点，不是纯OMML段落
                return false;
            }
        }
        
        // 通过所有检查，是纯OMML段落
        return true;
    }

    // 处理包含OMML公式的段落对齐问题 - 全面解决方案
    // 处理包含OMML公式的段落居中对齐问题 - 确保纯OMML段落正确居中
    private void HandleOmmlParagraphAlignment(Paragraph paragraph, Cell cell)
    {
        try
        {
            // 确保段落本身居中对齐
            paragraph.ParagraphFormat.Alignment = ParagraphAlignment.Center;
            
            // 处理OMML公式
            NodeCollection ommlNodes = paragraph.GetChildNodes(NodeType.OfficeMath, true);
            foreach (OfficeMath omml in ommlNodes)
            {
                // 纯OMML段落使用Display模式
                omml.DisplayType = OfficeMathDisplayType.Display;
                omml.Justification = OfficeMathJustification.Center;
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    // 处理答案区域的表格灰色底纹
    private void ProcessAnswerAreaTables(Document doc)
    {
        try
        {
            // 获取所有段落
            List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
            
            // 存储END标记对应的空行数（与CopyAndProcessDocumentStep保持一致）
            var endMarkers = new List<string> { "【END1】", "【END2】", "【END3】", "【END4】" };
            
            // 找出所有答案段落及其对应的END标记
            var answerEndPairs = new List<(Paragraph answerPara, Paragraph endPara)>();
            
            // 先找出所有答案段落
            for (int i = 0; i < allParagraphs.Count; i++)
            {
                Paragraph para = allParagraphs[i];
                string text = para.GetText();
                
                if (text.Contains("【答案】"))
                {
                    // 查找对应的END标记
                    for (int j = i + 1; j < allParagraphs.Count; j++)
                    {
                        Paragraph endPara = allParagraphs[j];
                        string endText = endPara.GetText().Trim();
                        
                        // 检查是否是纯END标记段落
                        foreach (string endMarker in endMarkers)
                        {
                            if (endText == endMarker || endText == endMarker + "\r")
                            {
                                answerEndPairs.Add((para, endPara));
                                goto NextAnswer; // 找到对应的END标记后跳出循环
                            }
                        }
                    }
                    NextAnswer:;
                }
            }
            
            // 处理每对答案和END标记之间的表格
            foreach (var (answerPara, endPara) in answerEndPairs)
            {
                ApplyGrayToTablesInAnswerArea(doc, answerPara, endPara, allParagraphs);
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    // 对答案区域内的表格应用灰色底纹
    private void ApplyGrayToTablesInAnswerArea(Document doc, Paragraph answerPara, Paragraph endPara, List<Paragraph> allParagraphs)
    {
        try
        {
            // 找到答案段落和END段落的索引
            int answerIndex = allParagraphs.IndexOf(answerPara);
            int endIndex = allParagraphs.IndexOf(endPara);
            
            if (answerIndex < 0 || endIndex < 0 || answerIndex >= endIndex)
            {
                return; // 索引无效
            }
            
            // 获取文档中的所有表格
            List<Table> allTables = doc.GetChildNodes(NodeType.Table, true).Cast<Table>().ToList();
            
            // 找出位于答案区域内的表格
            foreach (Table table in allTables)
            {
                if (IsTableInAnswerArea(table, answerPara, endPara, allParagraphs, answerIndex, endIndex))
                {
                    ApplyLightGrayShading(table);
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    // 判断表格是否在答案区域内
    private bool IsTableInAnswerArea(Table table, Paragraph answerPara, Paragraph endPara, 
        List<Paragraph> allParagraphs, int answerIndex, int endIndex)
    {
        try
        {
            // 通过文档位置判断表格是否在答案区域内
            // 获取表格的前一个段落
            Node tablePrevNode = table.PreviousSibling;
            while (tablePrevNode != null && !(tablePrevNode is Paragraph))
            {
                tablePrevNode = tablePrevNode.PreviousSibling;
            }
            
            // 获取表格的后一个段落
            Node tableNextNode = table.NextSibling;
            while (tableNextNode != null && !(tableNextNode is Paragraph))
            {
                tableNextNode = tableNextNode.NextSibling;
            }
            
            // 如果表格前后都有段落，检查这些段落是否在答案区域内
            if (tablePrevNode is Paragraph tablePrev && tableNextNode is Paragraph tableNext)
            {
                int tablePrevIndex = allParagraphs.IndexOf(tablePrev);
                int tableNextIndex = allParagraphs.IndexOf(tableNext);
                
                // 表格在答案区域内的条件：表格前的段落在answerIndex之后，表格后的段落在endIndex之前
                return tablePrevIndex >= answerIndex && tableNextIndex <= endIndex;
            }
            
            // 如果只有前面的段落，检查是否在答案区域内
            if (tablePrevNode is Paragraph tablePrevOnly)
            {
                int tablePrevIndex = allParagraphs.IndexOf(tablePrevOnly);
                return tablePrevIndex >= answerIndex && tablePrevIndex < endIndex;
            }
            
            // 如果只有后面的段落，检查是否在答案区域内
            if (tableNextNode is Paragraph tableNextOnly)
            {
                int tableNextIndex = allParagraphs.IndexOf(tableNextOnly);
                return tableNextIndex > answerIndex && tableNextIndex <= endIndex;
            }
            
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 对表格应用浅灰色底纹（与Word中白色列下面第一个灰色相同）
    private void ApplyLightGrayShading(Table table)
    {
        try
        {
            // 遍历表格中的所有单元格
            foreach (Row row in table.Rows)
            {
                foreach (Cell cell in row.Cells)
                {
                    // 设置单元格的浅灰色底纹
                    // 使用RGB(242, 242, 242)，这是Word中白色列下面第一个灰色的颜色值
                    cell.CellFormat.Shading.BackgroundPatternColor = System.Drawing.Color.FromArgb(242, 242, 242);
                    cell.CellFormat.Shading.ForegroundPatternColor = System.Drawing.Color.FromArgb(242, 242, 242);
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }
    
    // 处理包含OMML公式的段落居左对齐
    private void ProcessOmmlParagraphLeftAlignment(Paragraph paragraph, Cell cell)
    {
        try
        {
            // 检查段落是否包含OMML公式
            if (!ContainsOmmlFormula(paragraph)) return;
            
            if (IsPureOmmlParagraph(paragraph))
            {
                // 纯OMML段落：完整的特殊处理，确保正确对齐
                HandleOmmlParagraphLeftAlignment(paragraph, cell);
            }
            // 普通段落（包括混合内容段落）：完全不处理，使用正常的段落对齐
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }
    
    // 处理包含OMML公式的段落居左对齐问题 - 确保纯OMML段落正确居左
    private void HandleOmmlParagraphLeftAlignment(Paragraph paragraph, Cell cell)
    {
        try
        {
            // 确保段落本身居左对齐
            paragraph.ParagraphFormat.Alignment = ParagraphAlignment.Left;
            
            // 处理OMML公式
            NodeCollection ommlNodes = paragraph.GetChildNodes(NodeType.OfficeMath, true);
            foreach (OfficeMath omml in ommlNodes)
            {
                // 纯OMML段落使用Display模式
                omml.DisplayType = OfficeMathDisplayType.Display;
                omml.Justification = OfficeMathJustification.Left;
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }
}
