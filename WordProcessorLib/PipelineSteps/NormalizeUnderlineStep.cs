using System.Text.RegularExpressions;
using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 标准化填空步骤（实现IPipelineStep接口）
/// 将长度小于10个空格的下划线补充到10个空格长度
/// </summary>
public sealed class NormalizeUnderlineStep : IPipelineStep
{
    private const int MinBlankLength = 10; // 最小填空长度（空格数）

    /// <summary>
    /// 执行标准化填空处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中所有的段落节点
            NodeCollection paragraphs = doc.GetChildNodes(NodeType.Paragraph, true);

            foreach (Paragraph paragraph in paragraphs)
            {
                // 跳过页眉页脚
                if (IsInHeaderFooter(paragraph))
                    continue;

                // 处理段落中的连续下划线区域
                ProcessParagraphBlanks(paragraph);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    /// <param name="node">当前文档节点</param>
    /// <returns>true：属于页眉页脚内容；false：属于正文内容</returns>
    private static bool IsInHeaderFooter(Node node)
    {
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 处理段落中的连续下划线区域
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    private void ProcessParagraphBlanks(Paragraph paragraph)
    {
        var runs = paragraph.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();
        if (runs.Count == 0) return;

        var underlineGroups = FindUnderlineGroups(runs);

        foreach (var group in underlineGroups)
        {
            ProcessUnderlineGroup(group);
        }
    }

    /// <summary>
    /// 查找段落中的连续下划线区域
    /// </summary>
    /// <param name="runs">段落中的所有Run</param>
    /// <returns>连续下划线区域的列表</returns>
    private List<List<Run>> FindUnderlineGroups(List<Run> runs)
    {
        var groups = new List<List<Run>>();
        var currentGroup = new List<Run>();

        foreach (var run in runs)
        {
            if (HasUnderlineFormat(run))
            {
                currentGroup.Add(run);
            }
            else
            {
                // 遇到非下划线Run，结束当前组
                if (currentGroup.Count > 0)
                {
                    groups.Add(currentGroup);
                    currentGroup = new List<Run>();
                }
            }
        }

        // 处理最后一组
        if (currentGroup.Count > 0)
        {
            groups.Add(currentGroup);
        }

        return groups;
    }

    /// <summary>
    /// 检查Run是否有下划线格式
    /// </summary>
    /// <param name="run">Run节点</param>
    /// <returns>是否有下划线格式</returns>
    private bool HasUnderlineFormat(Run run)
    {
        return run.Font.Underline != Underline.None;
    }

    /// <summary>
    /// 处理一个连续的下划线区域
    /// </summary>
    /// <param name="group">连续的下划线Run组</param>
    private void ProcessUnderlineGroup(List<Run> group)
    {
        if (group.Count == 0) return;

        // 检查整个区域是否包含文字
        bool hasText = false;
        int totalLength = 0;

        foreach (var run in group)
        {
            string text = run.Text ?? "";
            totalLength += text.Length;

            // 如果包含非空白字符，则跳过整个区域
            if (!Regex.IsMatch(text, @"^\s*$"))
            {
                hasText = true;
                break;
            }
        }

        // 如果包含文字，跳过整个区域
        if (hasText) return;

        // 如果总长度已经>=10，不需要处理
        if (totalLength >= MinBlankLength) return;

        // 补充到10个空格
        NormalizeUnderlineGroup(group, totalLength);
    }

    /// <summary>
    /// 将下划线区域标准化为10个空格
    /// </summary>
    /// <param name="group">下划线Run组</param>
    /// <param name="currentLength">当前总长度</param>
    private void NormalizeUnderlineGroup(List<Run> group, int currentLength)
    {
        if (group.Count == 0) return;

        // 清空除第一个Run外的所有Run
        for (int i = 1; i < group.Count; i++)
        {
            group[i].Text = "";
        }

        // 将第一个Run设置为10个空格
        group[0].Text = new string(' ', MinBlankLength);
    }
}