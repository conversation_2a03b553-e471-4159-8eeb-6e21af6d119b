using Aspose.Words;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;
using System.Text.RegularExpressions;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 删除题目来源步骤
/// 功能：删除题号段落紧邻上一个段落中的题目来源信息
/// 题目来源格式：段落文本由中文括号"（）"包含，且段落只含有括号和括号内容
/// 题号格式：段落开头的数字+中文点"．"
/// </summary>
public class RemoveQuestionSourceStep : IPipelineStep
{
    /// <summary>
    /// 实现接口核心方法
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 处理单个正文区域
    /// </summary>
    private void ProcessBody(Body body)
    {
        // 获取所有普通段落（排除页眉页脚和表格内段落）
        List<Paragraph> paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null && !IsInTable(p))
            .ToList();

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsQuestionParagraph(current))
            {
                ProcessQuestionParagraph(paragraphs, i);
            }
        }
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    private bool IsInTable(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }

    /// <summary>
    /// 判断是否为题号段落（严格匹配数字+中文点"．"格式）
    /// </summary>
    private bool IsQuestionParagraph(Paragraph para)
    {
        string? text = para.Range?.Text?.TrimStart();
        if (string.IsNullOrEmpty(text))
            return false;
            
        // 严格匹配题号格式：段落开头的数字+中文点"．"
        Regex questionPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
        return questionPattern.IsMatch(text);
    }

    /// <summary>
    /// 处理题号段落，检查并删除紧邻的题目来源段落
    /// </summary>
    private void ProcessQuestionParagraph(List<Paragraph> paragraphs, int questionIndex)
    {
        // 找到紧邻的上一个段落（跳过空段落）
        Paragraph? adjacentParagraph = FindAdjacentParagraph(paragraphs, questionIndex);
        
        if (adjacentParagraph != null && IsQuestionSourceParagraph(adjacentParagraph))
        {
            // 删除题目来源段落
            adjacentParagraph.Remove();
        }
    }

    /// <summary>
    /// 找到题号段落紧邻的上一个段落（严格的紧邻判断）
    /// </summary>
    private Paragraph? FindAdjacentParagraph(List<Paragraph> paragraphs, int questionIndex)
    {
        if (questionIndex <= 0) return null;

        // 从题号段落开始往前查找
        for (int i = questionIndex - 1; i >= 0; i--)
        {
            Paragraph para = paragraphs[i];
            
            if (IsEmptyParagraph(para))
            {
                // 空段落也算紧邻，继续往前查找
                continue;
            }
            else
            {
                // 找到第一个非空段落，这就是紧邻段落
                return para;
            }
        }
        
        return null;
    }

    /// <summary>
    /// 判断是否为空段落（支持各种Unicode空白字符）
    /// </summary>
    private bool IsEmptyParagraph(Paragraph para)
    {
        string text = para.GetText();
        
        // 使用自定义方法检查是否只包含空白字符
        if (!IsOnlyWhitespaces(text))
            return false;
        
        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    private bool HasImageOrShape(Paragraph para)
    {
        // 检查Shape节点（包含图片和其他形状）
        NodeCollection shapes = para.GetChildNodes(NodeType.Shape, true);
        if (shapes.Count > 0)
            return true;

        // 检查GroupShape节点（组合形状）
        NodeCollection groupShapes = para.GetChildNodes(NodeType.GroupShape, true);
        if (groupShapes.Count > 0)
            return true;

        // 检查OfficeMath节点（数学公式）
        NodeCollection mathNodes = para.GetChildNodes(NodeType.OfficeMath, true);
        if (mathNodes.Count > 0)
            return true;

        return false;
    }

    /// <summary>
    /// 判断段落是否为题目来源段落（跨Run智能识别）
    /// 格式：段落文本由中文括号"（）"包含，且段落只含有括号和括号内容
    /// 支持括号前有各种Unicode空格字符
    /// 不能有其他内容，包括图片、形状等
    /// </summary>
    private bool IsQuestionSourceParagraph(Paragraph para)
    {
        // 首先检查段落是否包含图片、形状或其他非文本内容
        if (HasNonTextContent(para))
            return false;

        // 构建段落完整文本信息
        var textInfo = BuildParagraphTextInfo(para);
        string fullText = textInfo.FullText;

        if (string.IsNullOrEmpty(fullText))
            return false;

        // 移除各种Unicode空格字符后再检查
        string trimmedText = RemoveAllWhitespaces(fullText);
        if (string.IsNullOrEmpty(trimmedText))
            return false;

        // 检查是否符合题目来源格式：（可选空格）（内容）（可选空格）
        // 使用Unicode空格类别\s匹配所有空白字符
        Regex sourcePattern = new Regex(@"^\s*（[^（）]*）\s*$", RegexOptions.Compiled);
        return sourcePattern.IsMatch(fullText);
    }

    /// <summary>
    /// 判断段落是否包含非文本内容（图片、形状、表格等）
    /// </summary>
    private bool HasNonTextContent(Paragraph para)
    {
        // 检查是否包含图片或形状
        if (HasImageOrShape(para))
            return true;

        // 检查是否包含其他非Run节点（如表格、书签等）
        NodeCollection allChildren = para.GetChildNodes(NodeType.Any, false);
        foreach (Node child in allChildren)
        {
            if (child.NodeType != NodeType.Run)
            {
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 构建段落Run映射信息
    /// </summary>
    private ParagraphTextInfo BuildParagraphTextInfo(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        return new ParagraphTextInfo
        {
            FullText = fullText,
            RunMap = runMap
        };
    }

    /// <summary>
    /// 移除所有Unicode空白字符（包括各种编码的空格）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>移除空白字符后的文本</returns>
    private string RemoveAllWhitespaces(string text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;
            
        // 使用正则表达式移除所有Unicode空白字符
        // \s 匹配所有Unicode空白字符，包括：
        // - 普通空格 (U+0020)
        // - 制表符 (U+0009)
        // - 换行符 (U+000A, U+000D)
        // - 全角空格 (U+3000)
        // - 不间断空格 (U+00A0)
        // - 零宽度空格 (U+200B)
        // - 以及其他Unicode空白字符
        return Regex.Replace(text, @"\s", "", RegexOptions.Compiled);
    }

    /// <summary>
    /// 检查文本是否只包含各种空白字符
    /// </summary>
    /// <param name="text">要检查的文本</param>
    /// <returns>如果只包含空白字符返回true</returns>
    private bool IsOnlyWhitespaces(string text)
    {
        if (string.IsNullOrEmpty(text))
            return true;
            
        // 检查是否整个字符串都是Unicode空白字符
        return Regex.IsMatch(text, @"^\s*$", RegexOptions.Compiled);
    }

    /// <summary>
    /// 段落文本信息类
    /// </summary>
    private class ParagraphTextInfo
    {
        public string FullText { get; set; } = "";
        public List<(int start, int end, Run run)> RunMap { get; set; } = new List<(int, int, Run)>();
    }
}
