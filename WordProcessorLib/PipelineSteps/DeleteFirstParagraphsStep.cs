using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// 删除文档开头的指定段落数量
// 功能特性：
// 1. 支持嵌套段落删除（如表格内的段落）
// 2. 安全索引保护机制
public class DeleteFirstParagraphsStep : IPipelineStep
{
    private readonly int _paragraphsToRemove;

    // 构造函数（参数校验）
    // paragraphsToRemove：需要删除的段落数量
    // ArgumentException：当参数小于1时抛出
    public DeleteFirstParagraphsStep(int paragraphsToRemove)
    {
        _paragraphsToRemove = paragraphsToRemove;
    }

    public bool Execute(Document doc, string filePath)
    {
        // 如果 _paragraphsToRemove < 1，则跳过执行此管道步骤
        if (_paragraphsToRemove < 1)
            return true;
        
        try
        {
            // 仅处理第一个节的正文内容（符合大部分业务场景）
            Body mainBody = doc.FirstSection.Body;

            // 获取所有段落节点（包含表格/文本框等容器中的段落）
            NodeCollection paragraphs = mainBody.GetChildNodes(NodeType.Paragraph, true);

            // 计算实际可删除数量（避免越界）
            int actualRemoval = Math.Min(_paragraphsToRemove, paragraphs.Count);

            // 逆向删除保证索引稳定性（从最后往前删）
            for (int i = actualRemoval - 1; i >= 0; i--)
            {
                Paragraph para = (Paragraph)paragraphs[i];

                // 执行删除操作
                mainBody.RemoveChild(para);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
}
