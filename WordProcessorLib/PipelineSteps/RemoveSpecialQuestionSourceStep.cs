using Aspose.Words;
using Aspose.Words.Tables;
using Aspose.Words.Drawing;
using WordProcessorLib.Interfaces;
using System.Text.RegularExpressions;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 删除特殊题目来源步骤
/// 功能：删除题号段落紧邻上一个段落中的"图片+题目来源"或"图片+题号+题目来源"或"【】+题目来源"信息
/// 特殊题目来源格式支持三种模式：
/// 模式1：段落包含图片+题目来源文本，题目来源由中文括号"（）"包含
/// 模式2：段落包含图片+题号+题目来源文本，题号格式为数字+中文点"．"
/// 模式3：段落包含【】+题目来源文本，题目来源由中文括号"（）"包含
/// 段落只能含有图片、题目来源文本（可选题号）和前后空格，其他内容会跳过处理
/// 题号格式：段落开头的数字+中文点"．"
/// </summary>
public class RemoveSpecialQuestionSourceStep : IPipelineStep
{
    /// <summary>
    /// 实现接口核心方法
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 处理单个正文区域
    /// </summary>
    private void ProcessBody(Body body)
    {
        // 获取所有普通段落（排除页眉页脚和表格内段落）
        List<Paragraph> paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null && !IsInTable(p))
            .ToList();

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsQuestionParagraph(current))
            {
                ProcessQuestionParagraph(paragraphs, i);
            }
        }
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    private bool IsInTable(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }

    /// <summary>
    /// 判断是否为题号段落（严格匹配数字+中文点"．"格式）
    /// </summary>
    private bool IsQuestionParagraph(Paragraph para)
    {
        string? text = para.Range?.Text?.TrimStart();
        if (string.IsNullOrEmpty(text))
            return false;
            
        // 严格匹配题号格式：段落开头的数字+中文点"．"
        Regex questionPattern = new Regex(@"^\d+．", RegexOptions.Compiled);
        return questionPattern.IsMatch(text);
    }

    /// <summary>
    /// 处理题号段落，检查并删除紧邻的特殊题目来源段落
    /// </summary>
    private void ProcessQuestionParagraph(List<Paragraph> paragraphs, int questionIndex)
    {
        // 找到紧邻的上一个段落（跳过空段落）
        Paragraph? adjacentParagraph = FindAdjacentParagraph(paragraphs, questionIndex);
        
        if (adjacentParagraph != null && IsSpecialQuestionSourceParagraph(adjacentParagraph))
        {
            // 删除特殊题目来源段落
            adjacentParagraph.Remove();
        }
    }

    /// <summary>
    /// 找到题号段落紧邻的上一个段落（严格的紧邻判断）
    /// </summary>
    private Paragraph? FindAdjacentParagraph(List<Paragraph> paragraphs, int questionIndex)
    {
        if (questionIndex <= 0) return null;

        // 从题号段落开始往前查找
        for (int i = questionIndex - 1; i >= 0; i--)
        {
            Paragraph para = paragraphs[i];
            
            if (IsEmptyParagraph(para))
            {
                // 空段落也算紧邻，继续往前查找
                continue;
            }
            else
            {
                // 找到第一个非空段落，这就是紧邻段落
                return para;
            }
        }
        
        return null;
    }

    /// <summary>
    /// 判断是否为空段落（支持各种Unicode空白字符）
    /// </summary>
    private bool IsEmptyParagraph(Paragraph para)
    {
        string text = para.GetText();
        
        // 使用自定义方法检查是否只包含空白字符
        if (!IsOnlyWhitespaces(text))
            return false;
        
        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    private bool HasImageOrShape(Paragraph para)
    {
        // 检查Shape节点（包含图片和其他形状）
        NodeCollection shapes = para.GetChildNodes(NodeType.Shape, true);
        if (shapes.Count > 0)
            return true;

        // 检查GroupShape节点（组合形状）
        NodeCollection groupShapes = para.GetChildNodes(NodeType.GroupShape, true);
        if (groupShapes.Count > 0)
            return true;

        // 检查OfficeMath节点（数学公式）
        NodeCollection mathNodes = para.GetChildNodes(NodeType.OfficeMath, true);
        if (mathNodes.Count > 0)
            return true;

        return false;
    }

    /// <summary>
    /// 判断段落是否为特殊题目来源段落（图片+题目来源 或 图片+题号+题目来源 或 【】+题目来源）
    /// 支持三种格式：
    /// 格式1：段落包含图片+题目来源文本，题目来源由中文括号"（）"包含
    /// 格式2：段落包含图片+题号+题目来源文本，题号为数字+中文点"．"
    /// 格式3：段落包含【】+题目来源文本，题目来源由中文括号"（）"包含
    /// 支持图片、题号、【】和括号前后有各种Unicode空格字符
    /// 段落只能含有图片、题目来源文本（可选题号/【】）和前后空格，其他内容会跳过
    /// </summary>
    private bool IsSpecialQuestionSourceParagraph(Paragraph para)
    {
        // 分析段落内容结构
        var paragraphContent = AnalyzeParagraphContent(para);
        
        // 必须包含题目来源文本
        if (!paragraphContent.HasQuestionSource)
            return false;
            
        // 不能包含其他非允许的内容
        if (paragraphContent.HasOtherContent)
            return false;
        
        // 检查是否符合任一识别模式：
        // 模式1&2：必须包含图片 + 题目来源
        // 模式3：必须包含【】前缀 + 题目来源（不需要图片）
        bool hasImageWithSource = paragraphContent.HasImage && paragraphContent.HasQuestionSource;
        bool hasBracketPrefixWithSource = paragraphContent.HasBracketPrefix && paragraphContent.HasQuestionSource;
        
        return hasImageWithSource || hasBracketPrefixWithSource;
    }

    /// <summary>
    /// 分析段落内容结构
    /// </summary>
    private ParagraphContentInfo AnalyzeParagraphContent(Paragraph para)
    {
        var info = new ParagraphContentInfo();
        
        // 检查是否包含图片
        info.HasImage = HasImageOrShape(para);
        
        // 构建段落文本信息（仅来自Run节点）
        var textInfo = BuildParagraphTextInfo(para);
        string fullText = textInfo.FullText;
        
        if (!string.IsNullOrEmpty(fullText))
        {
            // 检查是否符合题目来源格式，支持三种模式：
            // 模式1：（可选空格）（内容）（可选空格）
            // 模式2：（可选空格）题号（可选空格）（内容）（可选空格）
            // 模式3：（可选空格）【】（可选空格）（内容）（可选空格）
            // 使用Unicode空格类别\s匹配所有空白字符
            // 题号格式：数字+中文点"．"
            
            // 模式1：只有题目来源
            Regex sourceOnlyPattern = new Regex(@"^\s*（[^（）]*）\s*$", RegexOptions.Compiled);
            
            // 模式2：题号+题目来源
            Regex numberAndSourcePattern = new Regex(@"^\s*\d+．\s*（[^（）]*）\s*$", RegexOptions.Compiled);
            
            // 模式3：【】+题目来源
            Regex bracketPrefixAndSourcePattern = new Regex(@"^\s*【】\s*（[^（）]*）\s*$", RegexOptions.Compiled);
            
            info.HasQuestionSource = sourceOnlyPattern.IsMatch(fullText) || 
                                   numberAndSourcePattern.IsMatch(fullText) ||
                                   bracketPrefixAndSourcePattern.IsMatch(fullText);
            
            // 检查是否包含【】前缀
            info.HasBracketPrefix = bracketPrefixAndSourcePattern.IsMatch(fullText);
        }
        
        // 检查是否包含其他不允许的内容
        info.HasOtherContent = HasDisallowedContent(para);
        
        return info;
    }

    /// <summary>
    /// 检查段落是否包含不允许的内容
    /// 允许的内容：Run节点（文本）、Shape节点（图片）、GroupShape节点（组合图片）
    /// </summary>
    private bool HasDisallowedContent(Paragraph para)
    {
        NodeCollection allChildren = para.GetChildNodes(NodeType.Any, false);
        
        foreach (Node child in allChildren)
        {
            // 允许的节点类型
            if (child.NodeType == NodeType.Run || 
                child.NodeType == NodeType.Shape || 
                child.NodeType == NodeType.GroupShape)
            {
                continue;
            }
            
            // 其他节点类型都不允许（如表格、书签、数学公式等）
            return true;
        }
        
        return false;
    }

    /// <summary>
    /// 构建段落Run映射信息（仅处理文本内容）
    /// </summary>
    private ParagraphTextInfo BuildParagraphTextInfo(Paragraph paragraph)
    {
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        string fullText = "";
        List<(int start, int end, Run run)> runMap = new List<(int, int, Run)>();
        
        foreach (Run run in runs)
        {
            int start = fullText.Length;
            fullText += run.Text;
            int end = fullText.Length;
            runMap.Add((start, end, run));
        }
        
        return new ParagraphTextInfo
        {
            FullText = fullText,
            RunMap = runMap
        };
    }

    /// <summary>
    /// 移除所有Unicode空白字符（包括各种编码的空格）
    /// </summary>
    /// <param name="text">输入文本</param>
    /// <returns>移除空白字符后的文本</returns>
    private string RemoveAllWhitespaces(string text)
    {
        if (string.IsNullOrEmpty(text))
            return string.Empty;
            
        // 使用正则表达式移除所有Unicode空白字符
        // \s 匹配所有Unicode空白字符，包括：
        // - 普通空格 (U+0020)
        // - 制表符 (U+0009)
        // - 换行符 (U+000A, U+000D)
        // - 全角空格 (U+3000)
        // - 不间断空格 (U+00A0)
        // - 零宽度空格 (U+200B)
        // - 以及其他Unicode空白字符
        return Regex.Replace(text, @"\s", "", RegexOptions.Compiled);
    }

    /// <summary>
    /// 检查文本是否只包含各种空白字符
    /// </summary>
    /// <param name="text">要检查的文本</param>
    /// <returns>如果只包含空白字符返回true</returns>
    private bool IsOnlyWhitespaces(string text)
    {
        if (string.IsNullOrEmpty(text))
            return true;
            
        // 检查是否整个字符串都是Unicode空白字符
        return Regex.IsMatch(text, @"^\s*$", RegexOptions.Compiled);
    }

    /// <summary>
    /// 段落文本信息类
    /// </summary>
    private class ParagraphTextInfo
    {
        public string FullText { get; set; } = "";
        public List<(int start, int end, Run run)> RunMap { get; set; } = new List<(int, int, Run)>();
    }

    /// <summary>
    /// 段落内容分析信息类
    /// </summary>
    private class ParagraphContentInfo
    {
        /// <summary>
        /// 是否包含图片
        /// </summary>
        public bool HasImage { get; set; } = false;
        
        /// <summary>
        /// 是否包含题目来源文本
        /// </summary>
        public bool HasQuestionSource { get; set; } = false;
        
        /// <summary>
        /// 是否包含【】前缀
        /// </summary>
        public bool HasBracketPrefix { get; set; } = false;
        
        /// <summary>
        /// 是否包含其他不允许的内容
        /// </summary>
        public bool HasOtherContent { get; set; } = false;
    }
}
