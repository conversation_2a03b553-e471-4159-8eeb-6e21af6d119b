using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

public sealed class InsertTagBeforeEndStep : IPipelineStep
{
    private readonly HashSet<string> _field1Patterns;
    private readonly string _field2;
    private readonly int _lookBackCount;

    public InsertTagBeforeEndStep(
        IEnumerable<string> field1Patterns,
        string field2,
        int lookBackCount)
    {
        if (lookBackCount < 0)
            throw new ArgumentOutOfRangeException(nameof(lookBackCount), "前查段落数不能为负");

        _field1Patterns = new HashSet<string>(
            field1Patterns.Select(s => s.Trim()), 
            StringComparer.Ordinal);
        _field2 = field2?.Trim() ?? throw new ArgumentNullException(nameof(field2));
        _lookBackCount = lookBackCount;
    }

    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取主文档段落（排除页眉页脚表格等）
            var paragraphs = doc.GetChildNodes(NodeType.Paragraph, true)
                .OfType<Paragraph>()
                .Where(p => p.ParentStory.StoryType == StoryType.MainText)
                .ToList();

            // 反向遍历避免插入新段落影响索引
            for (int i = paragraphs.Count - 1; i >= 0; i--)
            {
                var para = paragraphs[i];
                var text = para.GetText().Trim();

                // 仅处理纯field1段落
                if (_field1Patterns.Contains(text))
                {
                    // 检查是否需要跳过
                    if (!HasField2Before(paragraphs, i))
                    {
                        // 执行克隆和替换
                        CloneAndReplace(para);
                    }
                }
            }
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"处理 {filePath} 失败: {ex.Message}");
            return false;
        }
    }

    // 检查前序段落是否存在field2
    private bool HasField2Before(IList<Paragraph> paragraphs, int currentIndex)
    {
        int start = Math.Max(0, currentIndex - _lookBackCount);
        
        // 遍历前查范围（不包含当前段落）
        for (int i = start; i < currentIndex; i++)
        {
            if (paragraphs[i].GetText().Trim() == _field2)
                return true;
        }
        return false;
    }

    // 执行克隆和替换操作
    private void CloneAndReplace(Paragraph para)
    {
        // 克隆段落并插入到原段落后面
        var clonedPara = (Paragraph)para.Clone(true);
        para.ParentNode.InsertAfter(clonedPara, para);

        // 替换原段落内容（保留第一个Run的格式）
        var firstRun = para.Runs.Count > 0 ? para.Runs[0] : null;

        // var paraNodes = para.GetChildNodes(NodeType.Run, false);
        // paraNodes[0].GetText().Replace(,_field2);
        
        // 清空所有现有Run
        para.Runs.Clear();

        // 创建新Run并应用格式
        var newRun = new Run(para.Document) { Text = _field2 };
        if (firstRun != null)
        {
            // 设置 newRun 的字体格式
            newRun.Font.Name = "XITS Math";
            newRun.Font.NameFarEast = firstRun.Font.NameFarEast;
            newRun.Font.Color = firstRun.Font.Color;
            newRun.Font.Size = firstRun.Font.Size;
        }
        para.AppendChild(newRun);
    }
}