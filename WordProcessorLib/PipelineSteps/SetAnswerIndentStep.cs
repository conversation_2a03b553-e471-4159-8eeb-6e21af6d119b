using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Math;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 设置答案缩进处理步骤（实现IPipelineStep接口）
/// 功能：对【答案】段落到END标签段落之间的所有段落进行缩进设置和空格处理
/// </summary>
public class SetAnswerIndentStep : IPipelineStep
{
    private readonly string _blankMode;

    /// <summary>
    /// 构造函数：初始化空格处理模式
    /// </summary>
    /// <param name="blankMode">空格处理模式。"blank.on"为启用段落开头空格删除；"blank.off"为关闭空格删除</param>
    public SetAnswerIndentStep(string blankMode = "blank.off")
    {
        _blankMode = blankMode ?? "blank.off";
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>是否处理成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取所有段落
            List<Paragraph> allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true).Cast<Paragraph>().ToList();
            
            // 找出所有答案段落及其对应的END标记
            var answerEndPairs = FindAnswerEndPairs(allParagraphs);
            
            if (answerEndPairs.Count == 0)
            {
                return true; // 没有答案区域，直接返回成功
            }

            // 处理每对答案和END标记之间的段落
            foreach (var (answerPara, endPara) in answerEndPairs)
            {
                ProcessAnswerAreaParagraphs(allParagraphs, answerPara, endPara);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 查找所有答案段落及其对应的END标记
    /// </summary>
    /// <param name="allParagraphs">所有段落列表</param>
    /// <returns>答案段落和END段落的配对列表</returns>
    private List<(Paragraph answerPara, Paragraph endPara)> FindAnswerEndPairs(List<Paragraph> allParagraphs)
    {
        var answerEndPairs = new List<(Paragraph answerPara, Paragraph endPara)>();
        var endMarkers = new List<string> { "【END1】", "【END2】", "【END3】", "【END4】" };

        // 先找出所有答案段落
        for (int i = 0; i < allParagraphs.Count; i++)
        {
            Paragraph para = allParagraphs[i];
            string text = para.GetText();

            if (text.Contains("【答案】"))
            {
                // 查找对应的END标记
                for (int j = i + 1; j < allParagraphs.Count; j++)
                {
                    Paragraph endPara = allParagraphs[j];
                    string endText = endPara.GetText().Trim();

                    // 检查是否是纯END标记段落
                    foreach (string endMarker in endMarkers)
                    {
                        if (endText == endMarker || endText == endMarker + "\r")
                        {
                            answerEndPairs.Add((para, endPara));
                            goto NextAnswer; // 找到对应的END标记后跳出循环
                        }
                    }
                }
                NextAnswer:;
            }
        }

        return answerEndPairs;
    }

    /// <summary>
    /// 处理答案区域内的段落缩进和空格
    /// </summary>
    /// <param name="allParagraphs">所有段落列表</param>
    /// <param name="answerPara">答案段落</param>
    /// <param name="endPara">END标记段落</param>
    private void ProcessAnswerAreaParagraphs(List<Paragraph> allParagraphs, Paragraph answerPara, Paragraph endPara)
    {
        try
        {
            // 找到答案段落和END段落的索引
            int answerIndex = allParagraphs.IndexOf(answerPara);
            int endIndex = allParagraphs.IndexOf(endPara);

            if (answerIndex < 0 || endIndex < 0 || answerIndex >= endIndex)
            {
                return; // 索引无效
            }

            // 处理答案段落到END段落之间的所有段落（包括答案段落和END段落）
            for (int i = answerIndex; i <= endIndex; i++)
            {
                Paragraph para = allParagraphs[i];
                
                // 1. 设置段落左缩进为0字符
                para.ParagraphFormat.LeftIndent = 0;
                
                // 2. 设置段落特殊格式为无（取消首行缩进等）
                para.ParagraphFormat.FirstLineIndent = 0;
                
                // 3. 如果启用空格删除模式，处理段落开头的空格
                if (_blankMode == "blank.on")
                {
                    RemoveLeadingSpacesFromParagraph(para);
                }
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 安全地删除段落开头的空格（正确处理OMML公式节点）
    /// </summary>
    /// <param name="para">要处理的段落</param>
    private void RemoveLeadingSpacesFromParagraph(Paragraph para)
    {
        try
        {
            // *** 关键修复：只处理段落的直接子节点，正确区分Run节点和OMML节点 ***
            bool foundNonSpace = false;
            List<Node> nodesToRemove = new List<Node>();
            
            // 获取段落的直接子节点（不包括深层嵌套的节点）
            NodeCollection childNodes = para.GetChildNodes(NodeType.Any, false);
            
            if (childNodes.Count == 0)
            {
                return;
            }

            // 遍历段落的直接子节点，正确处理Run和OMML节点
            for (int i = 0; i < childNodes.Count; i++)
            {
                Node currentNode = childNodes[i];
                
                if (foundNonSpace)
                {
                    // 已经找到非空格内容，停止处理
                    break;
                }

                if (currentNode is Run run)
                {
                    // 处理Run节点
                    if (string.IsNullOrEmpty(run.Text))
                    {
                        continue;
                    }

                    string originalText = run.Text;
                    string pattern = @"^[\s\u0020\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]+";
                    Match match = Regex.Match(originalText, pattern);
                    
                    if (match.Success && match.Length > 0)
                    {
                        string textWithoutLeadingSpaces = originalText.Substring(match.Length);
                        
                        if (match.Length == originalText.Length)
                        {
                            // 这个Run完全是空格，标记为删除
                            nodesToRemove.Add(run);
                        }
                        else
                        {
                            // 这个Run开头有空格，删除空格部分，保留后面的内容
                            run.Text = textWithoutLeadingSpaces;
                            foundNonSpace = true; // 找到非空格内容，停止处理
                        }
                    }
                    else
                    {
                        // 这个Run不以空格开头，说明段落开头的空格处理完毕
                        foundNonSpace = true;
                    }
                }
                else if (currentNode is OfficeMath officeMath)
                {
                    // *** 关键修复：处理OMML公式节点 ***
                    // 删除OMML公式内部的开头空格，然后停止段落级别的空格删除
                    RemoveLeadingSpacesFromOfficeMath(officeMath);
                    foundNonSpace = true; // 遇到OMML就停止段落级别的空格删除
                }
                else
                {
                    // 遇到其他类型的节点（如图片、表格等），停止空格删除
                    foundNonSpace = true;
                }
            }
            
            // 删除标记为删除的节点
            foreach (Node node in nodesToRemove)
            {
                node.Remove();
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }

    /// <summary>
    /// 删除OMML公式内部开头的空格
    /// </summary>
    /// <param name="officeMath">OMML公式节点</param>
    private void RemoveLeadingSpacesFromOfficeMath(OfficeMath officeMath)
    {
        try
        {
            // 获取OMML公式中的所有Run节点
            NodeCollection runs = officeMath.GetChildNodes(NodeType.Run, true);
            
            if (runs.Count == 0)
            {
                return;
            }

            // 只处理第一个包含文本的Run，删除其开头空格
            foreach (Run run in runs.OfType<Run>())
            {
                if (string.IsNullOrEmpty(run.Text))
                {
                    continue;
                }

                string originalText = run.Text;
                string pattern = @"^[\s\u0020\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]+";
                Match match = Regex.Match(originalText, pattern);
                
                if (match.Success && match.Length > 0)
                {
                    string textWithoutLeadingSpaces = originalText.Substring(match.Length);
                    
                    if (!string.IsNullOrEmpty(textWithoutLeadingSpaces))
                    {
                        // 删除开头空格，保留后面的内容
                        run.Text = textWithoutLeadingSpaces;
                    }
                    else
                    {
                        // 如果Run完全是空格，删除整个Run
                        run.Remove();
                    }
                }
                
                // 只处理第一个包含文本的Run，然后停止
                break;
            }
        }
        catch (Exception)
        {
            // 静默处理错误
        }
    }


}
