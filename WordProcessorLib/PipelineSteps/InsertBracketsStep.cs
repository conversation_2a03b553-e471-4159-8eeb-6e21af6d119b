using System.Text.RegularExpressions;
using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 插入括号步骤（实现IPipelineStep接口）
/// 智能识别选择题，如果缺少答案括号则插入标准化空括号
/// </summary>
public sealed class InsertBracketsStep : IPipelineStep
{
    /// <summary>
    /// 执行插入括号处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档中不在表格内的所有段落
            var paragraphs = GetNonTableParagraphs(doc);
            
            if (paragraphs.Count == 0)
            {
                return true;
            }

            // 查找所有选择题并处理空括号插入
            ProcessChoiceQuestions(paragraphs);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 获取文档中不在表格内的所有段落
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>段落列表</returns>
    private List<Paragraph> GetNonTableParagraphs(Document doc)
    {
        List<Paragraph> paragraphs = new List<Paragraph>();
        
        // 获取所有段落节点
        NodeCollection allParagraphs = doc.GetChildNodes(NodeType.Paragraph, true);
        
        foreach (Paragraph para in allParagraphs)
        {
            // 检查父节点是否为表格单元格
            bool isInTable = false;
            Node parent = para.ParentNode;
            while (parent != null)
            {
                if (parent is Aspose.Words.Tables.Cell)
                {
                    isInTable = true;
                    break;
                }
                parent = parent.ParentNode;
            }
            
            // 如果不在表格中，则添加到列表
            if (!isInTable)
            {
                paragraphs.Add(para);
            }
        }
        
        return paragraphs;
    }

    /// <summary>
    /// 处理文档中的所有选择题
    /// </summary>
    /// <param name="paragraphs">文档段落列表</param>
    private void ProcessChoiceQuestions(List<Paragraph> paragraphs)
    {
        // 题号匹配正则表达式（严格匹配数字+中文点）
        Regex questionNumberPattern = new Regex(@"^\s*(\d+)．", RegexOptions.Compiled);
        
        for (int i = 0; i < paragraphs.Count; i++)
        {
            string text = GetParagraphText(paragraphs[i]);
            
            // 跳过空段落
            if (string.IsNullOrWhiteSpace(text))
            {
                continue;
            }
            
            // 检查是否为题号段落
            Match questionMatch = questionNumberPattern.Match(text);
            if (questionMatch.Success)
            {
                // 找到题号，检查是否为选择题并处理
                string questionNumber = questionMatch.Groups[1].Value;
                ProcessPotentialChoiceQuestion(paragraphs, i, questionNumber);
            }
        }
    }

    /// <summary>
    /// 获取段落的纯文本内容
    /// </summary>
    private string GetParagraphText(Paragraph paragraph)
    {
        return paragraph.ToString(SaveFormat.Text).Trim();
    }

    /// <summary>
    /// 处理潜在的选择题
    /// </summary>
    /// <param name="paragraphs">文档段落列表</param>
    /// <param name="questionIndex">题号所在段落索引</param>
    /// <param name="questionNumber">题号</param>
    private void ProcessPotentialChoiceQuestion(List<Paragraph> paragraphs, int questionIndex, string questionNumber)
    {
        // 从题号后开始扫描，寻找选择题特征
        int optionAIndex = -1;
        int answerIndex = -1;
        
        // 向后扫描寻找"A．"和"【答案】"
        for (int i = questionIndex + 1; i < paragraphs.Count; i++)
        {
            string text = GetParagraphText(paragraphs[i]);
            
            // 跳过空段落
            if (string.IsNullOrWhiteSpace(text))
            {
                continue;
            }
            
            // 检查是否找到了"A．"段落
            if (optionAIndex == -1 && StartsWithOptionA(paragraphs[i]))
            {
                optionAIndex = i;
                continue;
            }
            
            // 检查是否找到了"【答案】"段落
            if (StartsWithAnswer(paragraphs[i]))
            {
                answerIndex = i;
                break;
            }
            
            // 如果遇到下一个题号，停止搜索
            if (Regex.IsMatch(text, @"^\s*\d+．"))
            {
                break;
            }
        }
        
        // 验证是否为有效的选择题
        if (IsValidChoiceQuestion(paragraphs, optionAIndex, answerIndex))
        {
            // 检查是否已有空括号，如果没有则插入
            if (!HasExistingBrackets(paragraphs, questionIndex, answerIndex))
            {
                InsertBrackets(paragraphs, questionIndex, optionAIndex);
            }
        }
    }

    /// <summary>
    /// 检查段落是否以"A．"开头（严格匹配）
    /// </summary>
    private bool StartsWithOptionA(Paragraph paragraph)
    {
        return StartsWithPattern(paragraph, @"^\s*A．");
    }

    /// <summary>
    /// 检查段落是否以"【答案】"开头
    /// </summary>
    private bool StartsWithAnswer(Paragraph paragraph)
    {
        return StartsWithPattern(paragraph, @"^\s*【答案】");
    }

    /// <summary>
    /// 检查段落是否匹配指定模式（处理Run分割问题）
    /// </summary>
    private bool StartsWithPattern(Paragraph paragraph, string pattern)
    {
        // 获取段落的完整文本
        string fullText = GetParagraphText(paragraph);
        if (Regex.IsMatch(fullText, pattern))
        {
            return true;
        }
        
        // 如果单个段落文本不匹配，检查前几个Run的合并文本
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        if (runs.Count <= 1) return false;
        
        string mergedText = "";
        for (int i = 0; i < Math.Min(3, runs.Count); i++)
        {
            Run run = (Run)runs[i];
            mergedText += run.Text;
            if (Regex.IsMatch(mergedText.Trim(), pattern))
            {
                return true;
            }
        }
        
        return false;
    }

    /// <summary>
    /// 验证是否为有效的选择题
    /// </summary>
    private bool IsValidChoiceQuestion(List<Paragraph> paragraphs, int optionAIndex, int answerIndex)
    {
        // 必须同时找到"A．"和"【答案】"段落
        if (optionAIndex == -1 || answerIndex == -1)
        {
            return false;
        }
        
        // "A．"必须在"【答案】"之前
        if (optionAIndex >= answerIndex)
        {
            return false;
        }
        
        // 验证【答案】段落格式：只有"【答案】"和大写字母
        string answerText = GetParagraphText(paragraphs[answerIndex]);
        return Regex.IsMatch(answerText, @"^\s*【答案】[A-Z]+\s*$");
    }

    /// <summary>
    /// 检查指定范围内是否已存在空括号
    /// </summary>
    private bool HasExistingBrackets(List<Paragraph> paragraphs, int questionIndex, int answerIndex)
    {
        // 空括号匹配正则表达式（参考NormalizeBracketStep）
        Regex bracketPattern = new Regex(@"([(（])[ 　\u00A0\u2003\u2009\u2000-\u200F]*([）)])", RegexOptions.Compiled);
        
        // 检查题号段落本身以及题号之后到【答案】之前的所有段落
        for (int i = questionIndex; i < answerIndex; i++)
        {
            // 跳过图片和表格段落（这里简化处理，主要检查文本段落）
            if (IsTextParagraph(paragraphs[i]))
            {
                // 检查段落文本是否包含空括号
                if (ParagraphContainsBrackets(paragraphs[i], bracketPattern))
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    /// <summary>
    /// 检查段落是否包含空括号（处理Run分割问题）
    /// </summary>
    private bool ParagraphContainsBrackets(Paragraph paragraph, Regex bracketPattern)
    {
        // 首先检查完整段落文本
        string fullText = GetParagraphText(paragraph);
        if (bracketPattern.IsMatch(fullText))
        {
            return true;
        }
        
        // 如果段落文本不匹配，检查是否存在跨Run的括号
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        if (runs.Count <= 1) return false;
        
        // 合并多个Run的文本进行检查（处理Run分割导致的括号识别问题）
        string mergedText = "";
        for (int i = 0; i < runs.Count; i++)
        {
            Run run = (Run)runs[i];
            mergedText += run.Text;
            
            // 检查当前合并的文本是否包含完整的空括号
            if (bracketPattern.IsMatch(mergedText))
            {
                return true;
            }
            
            // 如果合并文本过长，只保留最近的部分以提高效率
            if (mergedText.Length > 50)
            {
                mergedText = mergedText.Substring(Math.Max(0, mergedText.Length - 30));
            }
        }
        
        return false;
    }

    /// <summary>
    /// 判断是否为文本段落（非图片、非表格）
    /// </summary>
    private bool IsTextParagraph(Paragraph paragraph)
    {
        // 检查段落是否包含图片（Shape节点）
        NodeCollection shapes = paragraph.GetChildNodes(NodeType.Shape, true);
        if (shapes.Count > 0)
        {
            return false;
        }
        
        // 检查是否有实际文本内容
        string text = GetParagraphText(paragraph);
        return !string.IsNullOrWhiteSpace(text);
    }

    /// <summary>
    /// 插入标准化空括号
    /// </summary>
    private void InsertBrackets(List<Paragraph> paragraphs, int questionIndex, int optionAIndex)
    {
        // 从"A．"段落向上查找合适的插入位置
        Paragraph targetParagraph = null;
        
        for (int i = optionAIndex - 1; i > questionIndex; i--)
        {
            if (IsTextParagraph(paragraphs[i]))
            {
                targetParagraph = paragraphs[i];
                break;
            }
        }
        
        // 如果没找到合适的文本段落，使用题号段落
        if (targetParagraph == null)
        {
            targetParagraph = paragraphs[questionIndex];
        }
        
        // 在段落末尾插入标准化空括号
        InsertBracketAtEndOfParagraph(targetParagraph);
    }

    /// <summary>
    /// 在段落末尾插入标准化空括号
    /// </summary>
    private void InsertBracketAtEndOfParagraph(Paragraph paragraph)
    {
        // 标准化空括号格式（参考NormalizeBracketStep）
        string bracketText = "（    ）";
        
        // 创建新的Run节点
        Run bracketRun = new Run(paragraph.Document);
        bracketRun.Text = bracketText;
        
        // 设置括号字体为宋体（与InsertMultipleChoiceTagStep保持一致）
        bracketRun.Font.Name = "宋体";
        bracketRun.Font.NameFarEast = "宋体";
        
        // 在段落末尾添加空括号
        paragraph.AppendChild(bracketRun);
    }
} 