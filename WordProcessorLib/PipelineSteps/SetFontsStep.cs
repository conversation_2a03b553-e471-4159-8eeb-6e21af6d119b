using System.Text.RegularExpressions;
using Aspose.Words;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

// 字体替换处理步骤（实现IPipelineStep接口）
// 功能：通过构造函数接收中西文字体配置，独立替换文档字体
public class SetFontsStep : IPipelineStep
{
    // 字段声明
    private readonly string _latinFont;         // 西文字体配置（null表示不替换）
    private readonly string _cjkFont;           // 中文字体配置（null表示不替换）
    private readonly string _fontSize;          // 中文字号配置（null表示不替换）
    private readonly bool _skipTitle;           // 是否跳过标题字体替换

    // 构造函数：初始化字体配置
    // latinFont：西文字体名称。非空：替换西文字体；null 或空字符串：保留原字体
    // cjkFont：中文字体名称。非空：替换中文字体；null 或空字符串：保留原字体
    // skipTitle：是否跳过标题的字体替换，true为跳过，false为不跳过
    public SetFontsStep(string? latinFont, string? cjkFont, string? FontSize, bool skipTitle)
    {
        // 空值处理逻辑：输入空值或空白字符转为null
        _latinFont = string.IsNullOrWhiteSpace(latinFont) ? null : latinFont;
        _cjkFont   = string.IsNullOrWhiteSpace(cjkFont) ? null : cjkFont;
        _fontSize  = string.IsNullOrWhiteSpace(FontSize) ? null : FontSize;
        _skipTitle = skipTitle;
    }

    // 主执行方法：文档处理入口
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 识别标题段落（使用局部变量确保线程安全）
            HashSet<Paragraph> titleParagraphs = new HashSet<Paragraph>();
            if (_skipTitle)
            {
                titleParagraphs = IdentifyTitleParagraphs(doc);
            }

            // 获取文档所有节点（深度递归遍历）
            NodeCollection nodes = doc.GetChildNodes(NodeType.Any, true);

            // 遍历处理每个节点
            foreach (Node node in nodes)
            {
                // 跳过页眉页脚内容
                if (IsInHeaderFooter(node)) continue;

                // 根据节点类型分发处理
                ProcessNode(node, titleParagraphs);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 检查节点是否位于页眉/页脚区域
    // node：当前文档节点；true：属于页眉页脚内容；false：属于正文内容
    private static bool IsInHeaderFooter(Node node)
    {
        // 通过查找最近的HeaderFooter祖先节点判断位置
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    // 节点处理路由中心
    // node：当前文档节点
    // titleParagraphs：标题段落集合
    private void ProcessNode(Node node, HashSet<Paragraph> titleParagraphs)
    {
        // 使用类型模式匹配分发处理
        switch (node)
        {
            case Paragraph para:    // 处理段落（设置换行符字体）
                // 跳过标题段落
                if (_skipTitle && titleParagraphs.Contains(para))
                    break;

                // 检查是否是题号段落，如果是则特殊处理
                if (IsQuestionNumberParagraph(para))
                {
                    ProcessQuestionNumberParagraph(para);
                }
                else
                {
                    SetParagraphBreakFont(para);
                }
                break;

            case Run run:       // 处理文本片段
                // 跳过标题段落中的Run
                if (_skipTitle && IsRunInTitleParagraph(run, titleParagraphs))
                    break;

                // 检查是否是题号段落中的Run，如果是则跳过（已在段落级别处理）
                if (IsRunInQuestionNumberParagraph(run))
                {
                    // 题号段落已在段落级别处理，跳过Run级别处理
                    break;
                }
                else
                {
                    SetRunFont(run);  // 原有逻辑
                }
                break;

            // case Aspose.Words.Tables.Table table:       // 处理表格结构
            //     ProcessTableFont(table);
            //     break;
        }
    }
    
    // 设置文本片段字体（条件替换逻辑）
    // run：文本运行对象
    private void SetRunFont(Run run)
    {
        ApplyFontSettings(run);
    }
    
    // 设置段落换行符字体（条件替换逻辑）
    // para：段落对象
    private void SetParagraphBreakFont(Paragraph para)
    {
        // 西文字体替换条件判断
        if (_latinFont != null)
            para.ParagraphBreakFont.Name = _latinFont;
        
        // 中文字体替换条件判断
        if (_cjkFont != null)
            para.ParagraphBreakFont.NameFarEast = _cjkFont;
        
        // 字体大小替换条件判断
        if (_fontSize != null)
            para.ParagraphBreakFont.Size = FontSizeConverter.GetPointSize(_fontSize);
    }

    // 处理表格字体（四层遍历结构）【多余代码】
    // table：表格对象
    // private void ProcessTableFont(Aspose.Words.Tables.Table table)
    // {
    //     // 遍历表格结构：行 → 单元格 → 段落 → 文本
    //     foreach (Aspose.Words.Tables.Row row in table.Rows)
    //     {
    //         foreach (Aspose.Words.Tables.Cell cell in row.Cells)
    //         {
    //             foreach (Paragraph para in cell.Paragraphs)
    //             {
    //                 foreach (Run run in para.Runs)
    //                 {
    //                     SetRunFont(run);  // 复用字体设置逻辑
    //                 }
    //             }
    //         }
    //     }
    // }

    /// <summary>
    /// 识别文档中的标题段落
    /// 标题特征：文档最前面的几行，居中对齐，以空行结束
    /// 扩展：每个section开头的连续居中段落也被视为标题
    /// 扩展：每个页面开头（分页符后）的连续居中段落也被视为标题
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>识别到的标题段落集合</returns>
    private HashSet<Paragraph> IdentifyTitleParagraphs(Document doc)
    {
        var titleParagraphs = new HashSet<Paragraph>();

        // 获取文档中所有非表格段落
        var paragraphs = doc.GetChildNodes(NodeType.Paragraph, true)
            .Cast<Paragraph>()
            .Where(p => !IsInHeaderFooter(p))
            .ToList();

        if (paragraphs.Count == 0) return titleParagraphs;

        // 1. 处理文档开头的标题段落（原有逻辑）
        IdentifyConsecutiveCenteredParagraphs(paragraphs, 0, titleParagraphs);

        // 2. 处理每个section开头的标题段落
        IdentifyTitlesAtSectionStarts(paragraphs, titleParagraphs);

        // 3. 处理每个分页符后的标题段落
        IdentifyTitlesAfterPageBreaks(paragraphs, titleParagraphs);

        return titleParagraphs;
    }

    /// <summary>
    /// 识别从指定位置开始的连续居中段落作为标题
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="startIndex">开始检查的索引位置</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyConsecutiveCenteredParagraphs(List<Paragraph> paragraphs, int startIndex, HashSet<Paragraph> titleParagraphs)
    {
        if (startIndex >= paragraphs.Count) return;

        for (int i = startIndex; i < paragraphs.Count; i++)
        {
            Paragraph para = paragraphs[i];

            // 检查是否为居中对齐
            if (para.ParagraphFormat.Alignment == ParagraphAlignment.Center)
            {
                titleParagraphs.Add(para);

                // 如果是空段落且居中，认为标题区域结束
                if (string.IsNullOrWhiteSpace(para.GetText().Trim()))
                {
                    break;
                }
            }
            else
            {
                // 遇到非居中段落，标题区域结束
                break;
            }
        }
    }

    /// <summary>
    /// 识别每个section开头的标题段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyTitlesAtSectionStarts(List<Paragraph> paragraphs, HashSet<Paragraph> titleParagraphs)
    {
        // 遍历所有段落，找到每个section的第一个段落
        for (int i = 1; i < paragraphs.Count; i++) // 从1开始，因为第0个段落已经在文档开头处理过了
        {
            Paragraph para = paragraphs[i];

            // 检查当前段落是否是section的第一个段落
            if (IsFirstParagraphInSection(para))
            {
                // 从这个段落开始识别连续的居中段落作为标题
                IdentifyConsecutiveCenteredParagraphs(paragraphs, i, titleParagraphs);
            }
        }
    }

    /// <summary>
    /// 识别每个分页符后的标题段落
    /// </summary>
    /// <param name="paragraphs">段落列表</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    private void IdentifyTitlesAfterPageBreaks(List<Paragraph> paragraphs, HashSet<Paragraph> titleParagraphs)
    {
        // 遍历所有段落，找到每个分页符后的第一个段落
        for (int i = 1; i < paragraphs.Count; i++) // 从1开始，因为第0个段落已经在文档开头处理过了
        {
            Paragraph para = paragraphs[i];
            Paragraph prevPara = paragraphs[i - 1];

            // 检查前一个段落是否有分页符
            if (HasPageBreakBefore(para) || HasPageBreakAfter(prevPara))
            {
                // 从这个段落开始识别连续的居中段落作为标题
                IdentifyConsecutiveCenteredParagraphs(paragraphs, i, titleParagraphs);
            }
        }
    }

    /// <summary>
    /// 检查段落是否是section的第一个段落
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否是section的第一个段落</returns>
    private bool IsFirstParagraphInSection(Paragraph para)
    {
        // 获取段落所在的section
        Section section = para.ParentSection;
        if (section == null) return false;

        // 获取section的第一个段落
        Body body = section.Body;
        if (body == null || body.FirstParagraph == null) return false;

        // 检查当前段落是否是section的第一个段落
        return para == body.FirstParagraph;
    }

    /// <summary>
    /// 检查段落是否有分页符前缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符前缀</returns>
    private bool HasPageBreakBefore(Paragraph para)
    {
        return para.ParagraphFormat.PageBreakBefore;
    }

    /// <summary>
    /// 检查段落是否有分页符后缀
    /// </summary>
    /// <param name="para">段落对象</param>
    /// <returns>是否有分页符后缀</returns>
    private bool HasPageBreakAfter(Paragraph para)
    {
        // 检查段落中是否包含分页符
        string text = para.ToString(SaveFormat.Text);

        // 检查是否有分页符字符
        if (text.Contains("\f"))
            return true;

        // 检查段落中的所有Run
        foreach (Node node in para.GetChildNodes(NodeType.Run, true))
        {
            if (node is Run run && run.Text != null && run.Text.Contains("\f"))
                return true;
        }

        // 检查是否有分页符
        return false;
    }
    
    /// <summary>
    /// 检查Run是否属于标题段落
    /// </summary>
    /// <param name="run">Run节点</param>
    /// <param name="titleParagraphs">标题段落集合</param>
    /// <returns>是否属于标题段落</returns>
    private bool IsRunInTitleParagraph(Run run, HashSet<Paragraph> titleParagraphs)
    {
        // 向上查找父段落
        Node parent = run.ParentNode;
        while (parent != null && parent is not Paragraph)
        {
            parent = parent.ParentNode;
        }

        return parent is Paragraph para && titleParagraphs.Contains(para);
    }

    /// <summary>
    /// 检查段落是否是题号段落
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    /// <returns>是否是题号段落</returns>
    private bool IsQuestionNumberParagraph(Paragraph paragraph)
    {
        // 检查段落是否以题号开头（数字+中文点"．"）
        string paragraphText = paragraph.ToString(SaveFormat.Text).Trim();
        return Regex.IsMatch(paragraphText, @"^\d+．");
    }

    /// <summary>
    /// 检查Run是否属于题号段落
    /// </summary>
    /// <param name="run">Run节点</param>
    /// <returns>是否属于题号段落</returns>
    private bool IsRunInQuestionNumberParagraph(Run run)
    {
        // 获取所属段落
        Node parent = run.ParentNode;
        while (parent != null && parent is not Paragraph)
        {
            parent = parent.ParentNode;
        }

        if (parent is not Paragraph paragraph)
            return false;

        return IsQuestionNumberParagraph(paragraph);
    }

    /// <summary>
    /// 处理题号段落，保持标签字体不变
    /// </summary>
    /// <param name="paragraph">段落节点</param>
    private void ProcessQuestionNumberParagraph(Paragraph paragraph)
    {
        // 设置段落换行符字体
        SetParagraphBreakFont(paragraph);
        
        // 获取段落的完整文本
        string paragraphText = paragraph.ToString(SaveFormat.Text).Trim();
        
        // 使用正则表达式查找所有的"[XXX]"标签位置
        var matches = Regex.Matches(paragraphText, @"\[[^\]]*\]");
        
        if (matches.Count == 0)
        {
            // 没有标签，对所有Run应用原有逻辑
            foreach (Node node in paragraph.GetChildNodes(NodeType.Run, true))
            {
                if (node is Run run)
                    SetRunFont(run);
            }
            return;
        }
        
        // 创建标签位置映射
        var tagRanges = new List<(int start, int end)>();
        foreach (Match match in matches)
        {
            tagRanges.Add((match.Index, match.Index + match.Length));
        }
        
                 // 处理段落中的每个Run
         int currentPosition = 0;
         foreach (Node node in paragraph.GetChildNodes(NodeType.Run, true))
         {
             if (node is not Run run) continue;

             string runText = run.Text ?? "";
             if (string.IsNullOrEmpty(runText))
             {
                 continue;
             }
            
            int runStart = currentPosition;
            int runEnd = currentPosition + runText.Length;
            
            // 检查这个Run是否完全在标签范围内
            bool isCompletelyInTag = false;
            foreach (var (start, end) in tagRanges)
            {
                if (runStart >= start && runEnd <= end)
                {
                    isCompletelyInTag = true;
                    break;
                }
            }
            
            // 检查这个Run是否与任何标签有重叠
            bool hasTagOverlap = false;
            foreach (var (start, end) in tagRanges)
            {
                if (runStart < end && runEnd > start)
                {
                    hasTagOverlap = true;
                    break;
                }
            }
            
            if (isCompletelyInTag || (hasTagOverlap && (runText.Contains('[') || runText.Contains(']'))))
            {
                // 这个Run是标签的一部分，保持原字体
                // 不调用ApplyFontSettings
            }
            else
            {
                // 这个Run不是标签的一部分，应用新字体设置
                ApplyFontSettings(run);
            }
            
            currentPosition += runText.Length;
        }
    }

    /// <summary>
    /// 应用字体设置（提取的原有逻辑）
    /// </summary>
    /// <param name="run">Run节点</param>
    private void ApplyFontSettings(Run run)
    {
        // 西文字体替换条件判断
        if (_latinFont != null)
            run.Font.Name = _latinFont;
        
        // 中文字体替换条件判断
        if (_cjkFont != null)
            run.Font.NameFarEast = _cjkFont;
        
        if (_fontSize != null)
            run.Font.Size = FontSizeConverter.GetPointSize(_fontSize);
    }
}