using Aspose.Words;
using Aspose.Words.Drawing;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

// 段落间距调整步骤（完整实现版）
// 功能：精确调整目标段落前的空行数，并标准化空行格式
// 支持单个或多个标识符的匹配
public class SetEmptyParagraphsStep : IPipelineStep
{
    private readonly List<string> _targetMarkers;
    private readonly int _requiredSpaces;

    // 构造函数初始化参数（支持单个标识符，向后兼容）
    // targetMarker：需要匹配的段首标识（非空）
    // requiredSpaces：需要的标准空行数（≥0）
    // exception cref="ArgumentNullException"：目标字段为空时抛出
    // exception cref="ArgumentException"：空行数为负时抛出
    public SetEmptyParagraphsStep(string targetMarker, int requiredSpaces)
        : this(new List<string> { targetMarker }, requiredSpaces)
    {
    }

    // 构造函数初始化参数（支持多个标识符）
    // targetMarkers：需要匹配的段首标识列表（非空）
    // requiredSpaces：需要的标准空行数（≥0）
    // exception cref="ArgumentNullException"：目标字段列表为空时抛出
    // exception cref="ArgumentException"：空行数为负时抛出
    public SetEmptyParagraphsStep(List<string> targetMarkers, int requiredSpaces)
    {
        // 参数有效性验证
        if (targetMarkers == null || targetMarkers.Count == 0)
            throw new ArgumentNullException(nameof(targetMarkers));

        _targetMarkers = targetMarkers.Where(m => !string.IsNullOrWhiteSpace(m))
                                     .Select(m => m.Trim())
                                     .ToList();

        if (_targetMarkers.Count == 0)
            throw new ArgumentException("至少需要一个有效的目标标识符", nameof(targetMarkers));

        _requiredSpaces = requiredSpaces >= 0 ? requiredSpaces
            : throw new ArgumentException("空行数不能为负数", nameof(requiredSpaces));
    }

    // 实现接口核心方法
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有章节处理正文
            foreach (Section section in doc.Sections.OfType<Section>())
            {
                ProcessBody(section.Body);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 处理单个正文区域
    private void ProcessBody(Body body)
    {
        // 获取所有普通段落（排除页眉页脚和表格内段落）
        List<Paragraph> paragraphs = body.GetChildNodes(NodeType.Paragraph, true)
            .OfType<Paragraph>()
            .Where(p => p.ParentNode?.GetAncestor(typeof(HeaderFooter)) == null && !IsInTable(p))
            .ToList();

        // 逆向遍历防止索引变化
        for (int i = paragraphs.Count - 1; i >= 0; i--)
        {
            Paragraph current = paragraphs[i];
            if (IsTargetParagraph(current))
            {
                AdjustSpacesBefore(current);
            }
        }
    }

    /// <summary>
    /// 判断段落是否在表格内
    /// </summary>
    private bool IsInTable(Paragraph para)
    {
        Node parent = para.ParentNode;
        while (parent != null)
        {
            if (parent is Cell)
            {
                return true;
            }
            parent = parent.ParentNode;
        }
        return false;
    }
    
    // 判断是否为目标段落（段首精确匹配多个标识符）
    private bool IsTargetParagraph(Paragraph para)
    {
        string? text = para.Range?.Text.TrimStart();
        if (string.IsNullOrEmpty(text))
            return false;

        return _targetMarkers.Any(marker => text.StartsWith(marker));
    }

    // 调整目标段落前的空行
    private void AdjustSpacesBefore(Paragraph target)
    {
        CompositeNode parent = target.ParentNode;
        NodeCollection siblings = parent.GetChildNodes(NodeType.Any, false);
        int targetIndex = siblings.IndexOf(target);

        // 获取当前所有前置空行（含非标准空行）
        List<Node> existingSpaces = GetExistingSpaces(siblings, targetIndex);

        // 删除所有现有空行（避免格式不统一问题）
        RemoveSpaces(existingSpaces, existingSpaces.Count);
        
        // 添加所需数量的新空行（统一格式，无缩进）
        AddSpaces(parent, target, _requiredSpaces);
    }
    
    // 获取当前前置空行（包含非标准空行）
    private List<Node> GetExistingSpaces(NodeCollection siblings, int targetIndex)
    {
        List<Node> spaces = new List<Node>();
        if (targetIndex < 1) return spaces;

        // 向前查找所有连续空段落
        for (int i = targetIndex - 1; i >= 0; i--)
        {
            Node node = siblings[i];
            if (node is Paragraph p && IsSpaceParagraph(p))
            {
                spaces.Add(node);
            }
            else
            {
                break; // 遇到非空段落停止
            }
        }

        // 恢复原始顺序（从前往后）
        spaces.Reverse();
        return spaces;
    }

    // 添加缺失的空行
    private void AddSpaces(CompositeNode parent, Paragraph target, int count)
    {
        // 类型安全转换
        Document doc = parent.Document as Document 
            ?? throw new InvalidCastException("文档类型不兼容");

        for (int i = 0; i < count; i++)
        {
            Paragraph space = new Paragraph(doc);
            space.AppendChild(new Run(doc, ""));
            parent.InsertBefore(space, target);
        }
    }

    // 删除多余的空行
    private void RemoveSpaces(List<Node> existingSpaces, int count)
    {
        // 从后往前删除（保持索引稳定）
        for (int i = 0; i < count && existingSpaces.Count > 0; i++)
        {
            Node lastSpace = existingSpaces.Last();
            lastSpace.Remove();
            existingSpaces.Remove(lastSpace);
        }
    }

    // 判断是否为空行段落（包含各种空格）
    private bool IsSpaceParagraph(Paragraph para)
    {
        // 检查是否有非空白文本
        if (!string.IsNullOrWhiteSpace(para.GetText()))
            return false;
        
        // 检查段落中是否有图片或形状
        return !HasImageOrShape(para);
    }

    /// <summary>
    /// 判断段落是否包含图片或形状
    /// </summary>
    private bool HasImageOrShape(Paragraph para)
    {
        // 直接搜索段落中的 Shape 节点（Aspose.Words.Drawing.Shape）
        foreach (Shape shape in para.GetChildNodes(NodeType.Shape, true))
        {
            if (shape.ImageData.HasImage)
                return true;
        }
        return false;
    }
}