using Aspose.Words;
using WordProcessorLib.Interfaces;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 删除文档第一个节处理步骤（实现IPipelineStep接口）
/// 功能：删除Word文档的第一个Section节，适用于需要移除封面页或标题页的场景
/// </summary>
public sealed class RemoveFirstSectionStep : IPipelineStep
{
    /// <summary>
    /// 执行删除第一个节的处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 检查文档是否有足够的节数
            if (doc.Sections.Count <= 1)
            {
                // 如果只有一个或没有节，跳过处理避免文档结构损坏
                return true;
            }

            // 获取第一个节
            Section firstSection = doc.FirstSection;
            
            // 安全删除第一个节
            if (firstSection != null)
            {
                firstSection.Remove();
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
} 