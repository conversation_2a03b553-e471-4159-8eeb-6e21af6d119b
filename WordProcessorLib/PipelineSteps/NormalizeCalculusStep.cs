using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;
using System.Text;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 微积分符号标准化处理步骤（实现IPipelineStep接口）
/// 功能：遍历文档的公式节点，将指定的微积分符号中的 d 字符设置为非斜体
/// 作用：确保微积分符号（如dx、dy、df等）中的 d 在公式中显示为正体而非斜体，符合数学排版规范
/// </summary>
public class NormalizeCalculusStep : IPipelineStep
{
    // 字段声明
    private readonly List<string> _targetCalculusSymbols;  // 需要设置 d 为非斜体的微积分符号集合（使用List保证顺序）

    /// <summary>
    /// 构造函数：初始化目标微积分符号配置
    /// </summary>
    /// <param name="targetCalculusSymbols">需要设置 d 为非斜体的微积分符号列表</param>
    /// <remarks>
    /// 使用List存储符号名并按长度降序排列，确保长符号名优先匹配，支持严格匹配（区分大小写）
    /// 例如：["dx", "dy", "df"]
    /// </remarks>
    public NormalizeCalculusStep(List<string> targetCalculusSymbols)
    {
        // 将列表按长度降序排列，确保长符号名优先匹配
        _targetCalculusSymbols = targetCalculusSymbols != null
            ? targetCalculusSymbols.OrderByDescending(f => f.Length).ThenBy(f => f).ToList()
            : new List<string>();
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    /// <param name="doc">待处理的Word文档对象</param>
    /// <param name="filePath">文档文件路径（用于日志记录）</param>
    /// <returns>处理成功返回true，失败返回false</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 如果没有配置目标微积分符号，直接返回成功
            if (_targetCalculusSymbols.Count == 0)
            {
                return true;
            }

            // 获取文档所有节点（深度递归遍历）
            NodeCollection nodes = doc.GetChildNodes(NodeType.Any, true);

            // 遍历处理每个节点
            foreach (Node node in nodes)
            {
                // 跳过页眉页脚内容
                if (IsInHeaderFooter(node)) continue;

                // 根据节点类型分发处理
                ProcessNode(node);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    /// <param name="node">当前文档节点</param>
    /// <returns>true：属于页眉页脚内容；false：属于正文内容</returns>
    /// <remarks>
    /// 通过查找最近的HeaderFooter祖先节点判断位置，确保只处理正文中的公式
    /// </remarks>
    private static bool IsInHeaderFooter(Node node)
    {
        // 通过查找最近的HeaderFooter祖先节点判断位置
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 节点处理路由中心
    /// </summary>
    /// <param name="node">当前文档节点</param>
    /// <remarks>
    /// 使用类型模式匹配分发处理，只处理OfficeMath类型的节点（微软公式节点）
    /// </remarks>
    private void ProcessNode(Node node)
    {
        // 使用类型模式匹配分发处理，只处理微软公式节点
        switch (node)
        {
            case OfficeMath mathNode:
                ProcessOfficeMath(mathNode);
                break;
        }
    }

    /// <summary>
    /// 处理微软公式节点，查找并设置指定微积分符号中的 d 为非斜体
    /// </summary>
    /// <param name="mathNode">微软公式节点</param>
    /// <remarks>
    /// 核心处理逻辑：
    /// 1. 获取公式中所有Run节点
    /// 2. 分析文本内容，查找目标微积分符号
    /// 3. 处理跨Run的情况，智能合并文本进行匹配
    /// 4. 对匹配的符号中的 d 设置非斜体格式
    /// </remarks>
    private void ProcessOfficeMath(OfficeMath mathNode)
    {
        // 获取公式中所有文本运行节点（深度遍历）
        NodeCollection runs = mathNode.GetChildNodes(NodeType.Run, true);

        // 创建需要处理的Run节点列表（避免在遍历过程中修改集合导致的问题）
        // 作用：将待处理的Run节点复制到一个独立的列表中
        // 必要性：非常必要，因为后续操作可能会修改节点结构，在遍历原集合时直接修改会导致集合被修改的异常
        List<Run> runList = new List<Run>();
        foreach (Run run in runs)
        {
            runList.Add(run);
        }

        // 处理跨Run的微积分符号匹配
        ProcessCrossRunCalculusSymbols(runList);
    }

    /// <summary>
    /// 处理公式中的Run节点，查找并设置指定微积分符号中的 d 为非斜体
    /// </summary>
    /// <param name="runList">公式中的Run节点列表</param>
    /// <remarks>
    /// 按照用户思路实现：先处理单Run内的符号，再处理跨Run的符号
    /// 1. 第一步：遍历每个Run，处理单Run内的符号匹配
    /// 2. 第二步：处理跨Run的符号匹配
    /// </remarks>
    private void ProcessCrossRunCalculusSymbols(List<Run> runList)
    {
        if (runList.Count == 0) return;

        // 第一步：处理单Run内的微积分符号匹配
        ProcessSingleRunCalculusSymbols(runList);

        // 第二步：处理跨Run的微积分符号匹配
        ProcessCrossRunMatches(runList);
    }

    /// <summary>
    /// 处理单Run内的微积分符号匹配
    /// 修复：确保所有匹配都基于原始文本，避免分割过程中的文本变化影响后续匹配
    /// </summary>
    private void ProcessSingleRunCalculusSymbols(List<Run> runList)
    {
        // 简单直接的方法：遍历每个Run，查找并分割微积分符号
        for (int i = 0; i < runList.Count; i++)
        {
            var run = runList[i];
            string originalRunText = run.Text ?? "";
            if (string.IsNullOrEmpty(originalRunText)) continue;

            // 在原始文本中查找所有微积分符号匹配
            var matches = FindCalculusMatchesInText(originalRunText);
            if (matches.Count == 0) continue;

            // 关键修复：如果有多个匹配，使用一次性分割方法
            if (matches.Count > 1)
            {
                SplitRunForMultipleCalculusSymbols(run, matches, originalRunText);
            }
            else
            {
                // 单个匹配，使用原有方法，只分割 d 字符
                var match = matches[0];
                SplitRunForFunction(run, match.StartIndex, match.StartIndex + 1);
            }
        }
    }

    /// <summary>
    /// 处理跨Run的微积分符号匹配
    /// </summary>
    private void ProcessCrossRunMatches(List<Run> runList)
    {
        // 重新获取当前段落的所有Run（因为第一步可能已经分割了一些Run）
        if (runList.Count == 0) return;

        var paragraph = runList[0].GetAncestor(NodeType.Paragraph) as Paragraph;
        if (paragraph == null) return;

        var currentRuns = paragraph.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();
        if (currentRuns.Count == 0) return;

        // 构建完整文本和Run映射
        var runMappings = BuildRunMappings(currentRuns);
        string fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));

        if (string.IsNullOrEmpty(fullText)) return;

        // 查找跨Run的微积分符号匹配
        var crossRunMatches = FindCrossRunMatches(fullText, runMappings);

        // 处理每个跨Run匹配
        foreach (var match in crossRunMatches)
        {
            ProcessSingleCrossRunMatch(match, runMappings);
        }
    }

    private List<CalculusMatch> FindCalculusMatchesInText(string text)
    {
        var matches = new List<CalculusMatch>();

        foreach (string targetSymbol in _targetCalculusSymbols)
        {
            int searchStart = 0;
            while (true)
            {
                int foundIndex = text.IndexOf(targetSymbol, searchStart, StringComparison.Ordinal);
                if (foundIndex == -1) break;

                var match = new CalculusMatch
                {
                    SymbolName = targetSymbol,
                    StartIndex = foundIndex,
                    Length = targetSymbol.Length
                };

                matches.Add(match);

                searchStart = foundIndex + 1; // 继续搜索重叠匹配
            }
        }

        var sortedMatches = matches.OrderBy(m => m.StartIndex).ThenByDescending(m => m.Length).ToList();

        return sortedMatches;
    }

    /// <summary>
    /// 分割Run以处理微积分符号中的 d 字符
    /// 核心思路：将原Run分割为多个部分，只有 d 字符设置为非斜体
    /// </summary>
    /// <param name="run">需要分割的Run节点</param>
    /// <param name="functionStart">d字符开始位置</param>
    /// <param name="functionEnd">d字符结束位置</param>
    /// <remarks>
    /// 1. 将原Run克隆成多份，每份格式都与原本完全一致
    /// 2. 逐一设置每份的文本内容
    /// 3. 只有d字符部分设置为非斜体，其他部分格式不变
    /// 4. 避免复杂的格式继承问题
    /// </remarks>
    private void SplitRunForFunction(Run run, int functionStart, int functionEnd)
    {
        string originalText = run.Text ?? "";
        if (string.IsNullOrEmpty(originalText)) return;

        // 验证索引范围
        if (functionStart < 0 || functionEnd > originalText.Length || functionStart >= functionEnd)
        {
            return;
        }

        // 关键：在任何修改之前先克隆原始Run，保存原始状态
        Run originalRun = (Run)run.Clone(true);

        // 分割文本为三部分
        string beforeFunction = functionStart > 0 ? originalText.Substring(0, functionStart) : "";
        string functionText = originalText.Substring(functionStart, functionEnd - functionStart);
        string afterFunction = functionEnd < originalText.Length ? originalText.Substring(functionEnd) : "";

        // 创建文本段列表
        var segments = new List<(string text, bool isFunction)>();

        if (!string.IsNullOrEmpty(beforeFunction))
            segments.Add((beforeFunction, false));

        if (!string.IsNullOrEmpty(functionText))
            segments.Add((functionText, true));

        if (!string.IsNullOrEmpty(afterFunction))
            segments.Add((afterFunction, false));

        // 如果只有一个段，直接设置格式
        if (segments.Count == 1)
        {
            if (segments[0].isFunction)
            {
                run.Font.Italic = false; // d字符设置为非斜体
            }
            // 非函数部分保持原有格式
            return;
        }

        // 按照用户的清晰思路：将原Run用于第一个文本段
        run.Text = segments[0].text;
        if (segments[0].isFunction)
        {
            run.Font.Italic = false; // d字符设置为非斜体
        }
        // 非函数部分保持原有格式（不修改）

        // 跟踪最后插入的节点
        Run lastInsertedRun = run;

        // 遍历处理后续的文本段，从索引1开始
        for (int i = 1; i < segments.Count; i++)
        {
            // 关键：克隆原始Run（修改前的状态），保持完全一致的格式
            Run newRun = (Run)originalRun.Clone(true);
            newRun.Text = segments[i].text;

            if (segments[i].isFunction)
            {
                newRun.Font.Italic = false; // d字符设置为非斜体
            }
            // 非函数部分保持原始格式（不修改）

            // 在最后插入的节点后插入新节点
            run.ParentNode.InsertAfter(newRun, lastInsertedRun);

            // 更新最后插入的节点
            lastInsertedRun = newRun;
        }
    }

    /// <summary>
    /// 构建Run映射关系
    /// </summary>
    private List<RunMapping> BuildRunMappings(List<Run> runs)
    {
        var runMappings = new List<RunMapping>();
        int currentPosition = 0;

        foreach (var run in runs)
        {
            string runText = run.Text ?? "";
            runMappings.Add(new RunMapping
            {
                Run = run,
                StartPosition = currentPosition,
                EndPosition = currentPosition + runText.Length,
                Text = runText
            });
            currentPosition += runText.Length;
        }

        return runMappings;
    }

    /// <summary>
    /// 查找跨Run的微积分符号匹配
    /// 关键修复：只处理真正跨Run的符号，避免重复处理单Run内的符号
    /// </summary>
    private List<CalculusMatch> FindCrossRunMatches(string fullText, List<RunMapping> runMappings)
    {
        var matches = new List<CalculusMatch>();

        foreach (string targetSymbol in _targetCalculusSymbols)
        {
            int searchStart = 0;
            while (true)
            {
                int foundIndex = fullText.IndexOf(targetSymbol, searchStart, StringComparison.Ordinal);
                if (foundIndex == -1) break;

                // 关键修复：只处理真正跨Run的符号匹配
                if (IsMatchCrossRun(foundIndex, targetSymbol.Length, runMappings))
                {
                    matches.Add(new CalculusMatch
                    {
                        SymbolName = targetSymbol,
                        StartIndex = foundIndex,
                        Length = targetSymbol.Length
                    });
                }

                searchStart = foundIndex + 1;
            }
        }

        return matches.OrderBy(m => m.StartIndex).ThenByDescending(m => m.Length).ToList();
    }

    /// <summary>
    /// 检查匹配是否跨Run
    /// </summary>
    private bool IsMatchCrossRun(int matchStart, int matchLength, List<RunMapping> runMappings)
    {
        int matchEnd = matchStart + matchLength;

        // 查找匹配开始和结束位置所在的Run
        var startRun = runMappings.FirstOrDefault(rm => matchStart >= rm.StartPosition && matchStart < rm.EndPosition);
        var endRun = runMappings.FirstOrDefault(rm => matchEnd > rm.StartPosition && matchEnd <= rm.EndPosition);

        // 如果开始和结束在不同的Run中，则为跨Run匹配
        return startRun != null && endRun != null && startRun != endRun;
    }

    /// <summary>
    /// 处理单个跨Run匹配
    /// </summary>
    private void ProcessSingleCrossRunMatch(CalculusMatch match, List<RunMapping> runMappings)
    {
        // 对于微积分符号，我们需要特殊处理：只有 d 字符设置为非斜体
        if (!match.SymbolName.StartsWith("d") || match.SymbolName.Length <= 1)
        {
            return; // 只处理 d 开头且长度大于1的符号
        }

        // 找到匹配涉及的所有Run
        var affectedRuns = runMappings.Where(rm =>
            (match.StartIndex < rm.EndPosition && match.StartIndex + match.Length > rm.StartPosition))
            .ToList();

        foreach (var runMapping in affectedRuns)
        {
            // 计算在当前Run中的相对位置
            int matchStartInRun = Math.Max(0, match.StartIndex - runMapping.StartPosition);
            int matchEndInRun = Math.Min(runMapping.Text.Length,
                                          match.StartIndex + match.Length - runMapping.StartPosition);

            // 确保位置有效
            if (matchStartInRun < matchEndInRun && matchEndInRun <= (runMapping.Run.Text?.Length ?? 0))
            {
                // 对于跨Run的微积分符号，我们需要判断当前Run包含的是 d 还是变量
                int dPositionInMatch = 0; // d 在整个匹配中的位置（总是0）
                int dGlobalPosition = match.StartIndex + dPositionInMatch; // d 在全文中的位置

                // 判断当前Run是否包含 d 字符
                if (dGlobalPosition >= runMapping.StartPosition && dGlobalPosition < runMapping.EndPosition)
                {
                    // 当前Run包含 d 字符，只分割 d 字符部分
                    int dPositionInRun = dGlobalPosition - runMapping.StartPosition;
                    SplitRunForFunction(runMapping.Run, dPositionInRun, dPositionInRun + 1);
                }
                // 如果当前Run不包含 d 字符，则包含的是变量字符，不需要处理（保持原格式）
            }
        }
    }

    /// <summary>
    /// 处理多个微积分符号的分割
    /// 关键修复：避免分割过程中文本变化影响后续匹配
    /// </summary>
    /// <param name="run">要处理的Run</param>
    /// <param name="matches">所有微积分符号匹配</param>
    /// <param name="originalText">原始文本</param>
    private void SplitRunForMultipleCalculusSymbols(Run run, List<CalculusMatch> matches, string originalText)
    {
        // 按位置排序，确保处理顺序正确
        matches = matches.OrderBy(m => m.StartIndex).ToList();

        // 去除重叠匹配，保留最长匹配
        var nonOverlappingMatches = RemoveOverlappingCalculusMatches(matches);

        // 构建所有文本段，对于微积分符号，将 d 和后面的变量分开处理
        var segments = new List<(string text, bool isDCalculus)>();
        int currentPosition = 0;

        foreach (var match in nonOverlappingMatches)
        {
            // 添加符号前的文本（如果有）
            if (match.StartIndex > currentPosition)
            {
                string beforeText = originalText.Substring(currentPosition, match.StartIndex - currentPosition);
                if (!string.IsNullOrEmpty(beforeText))
                {
                    segments.Add((beforeText, false));
                }
            }

            // 对于微积分符号，分别处理 d 和后面的变量
            if (match.SymbolName.StartsWith("d") && match.SymbolName.Length > 1)
            {
                // 添加 d 字符（设为非斜体）
                segments.Add(("d", true));

                // 添加变量字符（保持原格式）
                string variableText = match.SymbolName.Substring(1);
                if (!string.IsNullOrEmpty(variableText))
                {
                    segments.Add((variableText, false));
                }
            }
            else
            {
                // 其他情况，整个符号作为一个段
                segments.Add((match.SymbolName, true));
            }

            currentPosition = match.StartIndex + match.Length;
        }

        // 添加最后剩余的文本（如果有）
        if (currentPosition < originalText.Length)
        {
            string remainingText = originalText.Substring(currentPosition);
            if (!string.IsNullOrEmpty(remainingText))
            {
                segments.Add((remainingText, false));
            }
        }

        // 如果没有段，直接返回
        if (segments.Count == 0) return;

        // 使用现有的分割逻辑处理所有段
        ApplyCalculusSegmentsToRun(run, segments);
    }

    /// <summary>
    /// 去除重叠的微积分符号匹配，保留最长匹配
    /// </summary>
    private List<CalculusMatch> RemoveOverlappingCalculusMatches(List<CalculusMatch> matches)
    {
        var result = new List<CalculusMatch>();

        foreach (var match in matches)
        {
            bool overlaps = result.Any(existing =>
                (match.StartIndex < existing.StartIndex + existing.Length) &&
                (match.StartIndex + match.Length > existing.StartIndex));

            if (!overlaps)
            {
                result.Add(match);
            }
        }

        return result;
    }

    /// <summary>
    /// 将微积分符号文本段应用到Run
    /// </summary>
    private void ApplyCalculusSegmentsToRun(Run run, List<(string text, bool isDCalculus)> segments)
    {
        if (segments.Count == 0) return;

        // 克隆原始Run，保存原始格式
        Run originalRun = (Run)run.Clone(true);

        // 如果只有一个段，直接设置格式
        if (segments.Count == 1)
        {
            run.Text = segments[0].text;
            if (segments[0].isDCalculus)
            {
                run.Font.Italic = false; // d字符设置为非斜体
            }
            // 非d字符部分保持原有格式
            return;
        }

        // 将原Run用于第一个文本段
        run.Text = segments[0].text;
        if (segments[0].isDCalculus)
        {
            run.Font.Italic = false; // d字符设置为非斜体
        }
        // 非d字符部分保持原有格式（不修改）

        // 跟踪最后插入的节点
        Run lastInsertedRun = run;

        // 遍历处理后续的文本段，从索引1开始
        for (int i = 1; i < segments.Count; i++)
        {
            // 关键：克隆原始Run（修改前的状态），保持完全一致的格式
            Run newRun = (Run)originalRun.Clone(true);
            newRun.Text = segments[i].text;

            if (segments[i].isDCalculus)
            {
                newRun.Font.Italic = false; // d字符设置为非斜体
            }
            // 非d字符部分保持原始格式（不修改）

            // 在最后插入的节点后插入新节点
            run.ParentNode.InsertAfter(newRun, lastInsertedRun);

            // 更新最后插入的节点
            lastInsertedRun = newRun;
        }
    }

    /// <summary>
    /// 微积分符号匹配结果
    /// </summary>
    private class CalculusMatch
    {
        public string SymbolName { get; set; } = "";
        public int StartIndex { get; set; }
        public int Length { get; set; }
    }

    /// <summary>
    /// Run映射信息
    /// </summary>
    private class RunMapping
    {
        public Run Run { get; set; } = null!;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public string Text { get; set; } = "";
    }
}