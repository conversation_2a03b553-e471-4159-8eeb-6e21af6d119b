using Aspose.Words;
using Aspose.Words.Tables;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

// 行距处理步骤（实现IPipelineStep接口）
// 功能：设置所有正文段落和表格中的段落为1.2倍行距
public class SetLineSpacingStep : IPipelineStep
{
    // 实现接口方法 - 文档处理主入口
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 遍历所有节（Section）
            foreach (Section section in doc.Sections)
            {
                // 只处理正文内容（排除页眉页脚）
                ProcessContainer(section.Body);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 递归处理内容容器（核心遍历方法）
    // container：内容容器（如Body、Cell等）
    private void ProcessContainer(CompositeNode container)
    {
        // 获取所有直接子节点（不递归）
        foreach (Node node in container.GetChildNodes(NodeType.Any, false))
        {
            switch (node.NodeType)
            {
                case NodeType.Paragraph:
                    SetLineSpacing((Paragraph)node);
                    break;
                    
                case NodeType.Table:
                    ProcessTable((Table)node);
                    break;
                    
                default:
                    // 递归处理其他容器节点
                    if (node is CompositeNode composite)
                        ProcessContainer(composite);
                    break;
            }
        }
    }

    // 处理表格结构（支持无限嵌套）
    private void ProcessTable(Table table)
    {
        foreach (Row row in table.Rows)
        {
            foreach (Cell cell in row.Cells)
            {
                // 递归处理单元格内容
                ProcessContainer(cell);
            }
        }
    }

    // 设置段落行距（核心方法）
    private void SetLineSpacing(Paragraph para)
    {
        ParagraphFormat format = para.ParagraphFormat;
        
        // 设置多倍行距模式
        format.LineSpacingRule = LineSpacingRule.Multiple;
        
        /* 
         * 行距计算逻辑：
         * - Word单倍行距基准值为12磅
         * - 1.2倍行距 = 12 * 1.2 = 14.4磅
         * - 精确到小数点后一位避免精度问题
         */
        format.LineSpacing = 14.4; 
        
        // 可选：清除段前段后间距
        format.SpaceBefore = 0;
        format.SpaceAfter = 0;
        
        // format.Style.Font.SizeBi = FontSizeConverter.GetPointSize("一号");
        // format.ClearFormatting();
    }
}