using Aspose.Words;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

// 在首行插入文件名步骤
public class InsertFileNameStep : IPipelineStep
{
    // 存储要删除的字段集合（改为纯字符串存储）
    private readonly HashSet<string> _fieldsToRemove;
    // 标记是否启用字段删除功能
    private readonly bool _enableFieldRemoval;
    // 存储双空格换行控制参数（6个参数对应前6个双空格）
    private readonly int[] _doubleSpaceActions;

    // 修改后的构造函数，增加6个双空格处理参数
    public InsertFileNameStep(IEnumerable<string> fieldsToRemove, bool enableFieldRemoval, 
        int space1, int space2, int space3, int space4, int space5, int space6)
    {
        // 初始化字段集合：转换为HashSet提高查询效率，空输入自动转为空集合
        _fieldsToRemove = new HashSet<string>(fieldsToRemove ?? new List<string>());
        _enableFieldRemoval = enableFieldRemoval;
        // 初始化双空格处理参数数组
        _doubleSpaceActions = new int[] { space1, space2, space3, space4, space5, space6 };
    }
    
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取无扩展名的文件名
            string fileName = Path.GetFileNameWithoutExtension(filePath);
            
            // ==== 新增逻辑：字段删除处理 ====
            if (_enableFieldRemoval && _fieldsToRemove.Count > 0)
            {
                // 遍历所有需要删除的字段（注意保留原始空格）
                foreach (string field in _fieldsToRemove)
                {
                    // 直接替换匹配的完整字段（包括空格）
                    fileName = fileName.Replace(field, "");
                }
            }

            // ==== 新增逻辑：双空格换行处理 ====
            // 查找所有双空格的位置
            var doubleSpacePositions = new List<int>();
            int pos = 0;
            while ((pos = fileName.IndexOf("  ", pos)) != -1)
            {
                doubleSpacePositions.Add(pos);
                pos += 2; // 移动到双空格之后
            }

            // 从后往前处理双空格（避免位置偏移），根据参数决定是否换行
            for (int i = Math.Min(doubleSpacePositions.Count, _doubleSpaceActions.Length) - 1; i >= 0; i--)
            {
                if (_doubleSpaceActions[i] == 1)
                {
                    // 将双空格替换为换行标记
                    fileName = fileName.Remove(doubleSpacePositions[i], 2).Insert(doubleSpacePositions[i], "\n");
                }
            }

            // ==== 根据换行符分割文件名，创建多个独立段落 ====
            string[] lines = fileName.Split('\n');
            var paragraphs = new List<Paragraph>();

            // 为每一行创建独立的段落
            foreach (string line in lines)
            {
                if (line.Length > 0) // 跳过空行
                {
                    // 创建新段落并配置格式
                    Paragraph para = new Paragraph(doc);
                    para.ParagraphFormat.FirstLineIndent = 0;
                    para.ParagraphFormat.Alignment = ParagraphAlignment.Center;

                    // 创建文本Run并设置格式
                    Run run = new Run(doc, line);
                    run.Font.Name = "XITS Math";
                    run.Font.NameFarEast = "宋体";
                    run.Font.Bold = true;
                    run.Font.Size = FontSizeConverter.GetPointSize("小二");
                    para.AppendChild(run);

                    // 设置段落标记的字体格式
                    para.ParagraphBreakFont.Name = "XITS Math";
                    para.ParagraphBreakFont.NameFarEast = "宋体";
                    para.ParagraphBreakFont.Bold = true;
                    para.ParagraphBreakFont.Size = FontSizeConverter.GetPointSize("小二");

                    paragraphs.Add(para);
                }
            }

            // 创建同样格式的空行段落
            Paragraph emptyPara = new Paragraph(doc);
            emptyPara.ParagraphFormat.FirstLineIndent = 0;
            emptyPara.ParagraphFormat.Alignment = ParagraphAlignment.Center;
            emptyPara.ParagraphBreakFont.Name = "XITS Math";
            emptyPara.ParagraphBreakFont.NameFarEast = "宋体";
            emptyPara.ParagraphBreakFont.Bold = true;
            emptyPara.ParagraphBreakFont.Size = FontSizeConverter.GetPointSize("小二");

            // 插入到文档开头
            Body body = doc.FirstSection.Body;
            
            if (body.HasChildNodes)
            {
                Node referenceNode = body.FirstChild;
                
                // 从前往后插入文件名段落，使用InsertAfter逻辑
                Node lastInserted = null;
                foreach (var para in paragraphs)
                {
                    if (lastInserted == null)
                    {
                        // 第一个段落插在原文档前面
                        body.InsertBefore(para, referenceNode);
                    }
                    else
                    {
                        // 后续段落插在前一个段落后面
                        body.InsertAfter(para, lastInserted);
                    }
                    lastInserted = para;
                }
                
                // 空行段落插在最后一个文件名段落后面
                if (lastInserted != null)
                {
                    body.InsertAfter(emptyPara, lastInserted);
                }
                else
                {
                    body.InsertBefore(emptyPara, referenceNode);
                }
            }
            else
            {
                // 空文档情况：依次添加所有段落
                foreach (var para in paragraphs)
                {
                    body.AppendChild(para);
                }
                // 最后添加空行段落
                body.AppendChild(emptyPara);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }
}