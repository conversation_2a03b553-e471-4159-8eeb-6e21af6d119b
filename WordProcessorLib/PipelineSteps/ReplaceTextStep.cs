using Aspose.Words;
using Aspose.Words.Math;
using Aspose.Words.Replacing;
using WordProcessorLib.Interfaces;
using System.Drawing;

namespace WordProcessorLib.PipelineSteps;

// 文档内容替换处理步骤（实现IPipelineStep接口）
public sealed class ReplaceTextStep : IPipelineStep
{
    /// <summary>
    /// 要进行替换的字典，键为要查找的文本，值为要替换成的文本
    /// </summary>
    private readonly Dictionary<string, string> _replaceDict;

    /// <summary>
    /// 构造函数，初始化替换字典
    /// </summary>
    /// <param name="replaceDict">替换规则字典</param>
    public ReplaceTextStep(Dictionary<string, string> replaceDict)
    {
        _replaceDict = replaceDict ?? throw new ArgumentNullException(nameof(replaceDict));
    }

    /// <summary>
    /// 执行文本替换处理
    /// </summary>
    /// <param name="doc">待处理的文档对象</param>
    /// <param name="filePath">当前处理的文件路径（用于日志）</param>
    /// <returns>处理是否成功</returns>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            if (_replaceDict == null || _replaceDict.Count == 0)
            {
                return true;
            }

            int totalReplacements = 0;

            // 1. 处理普通文本内容（主文档区域）
            ReplaceInMainDocument(doc);

            // 2. 处理OMML公式
            ReplaceInOmmlFormulas(doc);

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 在主文档内容中进行文本替换（不包括页眉页脚）
    /// 优化：按段落处理，支持跨Run的文本替换，完全保持格式
    /// 参考NormalizeMathFunctionStep和SetPunctuationStep的精细Run处理逻辑
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>替换次数</returns>
    private int ReplaceInMainDocument(Document doc)
    {
        int replacementCount = 0;

        // 获取文档中所有非表格段落（不在页眉页脚中）
        var paragraphs = doc.GetChildNodes(NodeType.Paragraph, true)
            .Cast<Paragraph>()
            .Where(p => !IsInHeaderFooter(p))
            .ToList();

        foreach (Paragraph paragraph in paragraphs)
        {
            replacementCount += ReplaceInParagraphPrecisely(paragraph);
        }

        return replacementCount;
    }
    
    /// <summary>
    /// 在段落中精细地进行文本替换，参考NormalizeMathFunctionStep和SetPunctuationStep的逻辑
    /// </summary>
    /// <param name="paragraph">段落对象</param>
    /// <returns>替换次数</returns>
    private int ReplaceInParagraphPrecisely(Paragraph paragraph)
    {
        int replacementCount = 0;
        
        // 获取段落中所有的Run节点（不在公式中）
        var runs = paragraph.GetChildNodes(NodeType.Run, true)
            .Cast<Run>()
            .Where(run => !IsInOfficeMath(run))
            .ToList();
            
        if (runs.Count == 0) return 0;

        // 对每个替换规则进行处理
        foreach (var kvp in _replaceDict)
        {
            string findText = kvp.Key;
            string replaceText = kvp.Value;
            
            // 处理跨Run替换（从后往前处理避免位置偏移）
            replacementCount += ProcessCrossRunReplacement(paragraph, findText, replaceText);
        }

        return replacementCount;
    }
    
    /// <summary>
    /// 处理跨Run替换，参考NormalizeMathFunctionStep的处理逻辑
    /// 特殊处理整个段落被替换的情况以避免格式丢失和额外空行
    /// </summary>
    private int ProcessCrossRunReplacement(Paragraph paragraph, string findText, string replaceText)
    {
        int replacementCount = 0;
        
        // 重新获取当前段落的所有Run（因为前面可能已经分割了一些Run）
        var currentRuns = paragraph.GetChildNodes(NodeType.Run, true)
            .Cast<Run>()
            .Where(run => !IsInOfficeMath(run))
            .ToList();
            
        if (currentRuns.Count == 0) return 0;

        // 构建完整文本和Run映射
        var runMappings = BuildRunMappings(currentRuns);
        string fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));

        if (string.IsNullOrEmpty(fullText)) return 0;

        // 查找所有匹配位置
        var matches = FindAllMatches(fullText, findText);
        
        // 检查是否为整个段落替换的特殊情况
        if (matches.Count == 1 && matches[0].start == 0 && matches[0].end == fullText.Length)
        {
            // 整个段落内容被替换，使用特殊处理避免格式丢失
            return ProcessWholeParagraphReplacement(paragraph, replaceText) ? 1 : 0;
        }
        
        // 从后往前处理每个匹配（非整个段落替换的情况）
        for (int i = matches.Count - 1; i >= 0; i--)
        {
            var (startIndex, endIndex) = matches[i];
            if (ProcessSingleMatch(runMappings, startIndex, endIndex, replaceText))
            {
                replacementCount++;
                
                // 重新构建映射（因为Run结构可能已改变）
                currentRuns = paragraph.GetChildNodes(NodeType.Run, true)
                    .Cast<Run>()
                    .Where(run => !IsInOfficeMath(run))
                    .ToList();
                runMappings = BuildRunMappings(currentRuns);
                fullText = string.Concat(runMappings.Select(rm => rm.Run.Text ?? ""));
            }
        }
        
        return replacementCount;
    }
    
    /// <summary>
    /// 处理整个段落替换的特殊情况，完全保持段落格式，避免产生额外空行
    /// </summary>
    private bool ProcessWholeParagraphReplacement(Paragraph paragraph, string replaceText)
    {
        try
        {
            // 保存段落的所有格式信息
            var originalFormat = new ParagraphFormatInfo(paragraph.ParagraphFormat);
            var originalFirstRunFormat = paragraph.Runs.Count > 0 ? new RunFormatInfo(paragraph.Runs[0].Font) : null;
            
            // 清除段落中所有的Run（但保持段落本身）
            paragraph.Runs.Clear();
            
            // 创建新的Run，继承原始格式
            Run newRun = new Run(paragraph.Document);
            if (originalFirstRunFormat != null)
            {
                originalFirstRunFormat.ApplyTo(newRun.Font);
            }
            newRun.Text = replaceText;
            
            // 将新Run添加到段落
            paragraph.AppendChild(newRun);
            
            // 确保段落格式完全恢复
            originalFormat.ApplyTo(paragraph.ParagraphFormat);
            
            return true;
        }
        catch
        {
            return false;
        }
    }
    
    /// <summary>
    /// 处理单个匹配，按照用户的精细要求实现
    /// </summary>
    private bool ProcessSingleMatch(List<RunMapping> runMappings, int startIndex, int endIndex, string replaceText)
    {
        // 找到涉及的Run范围
        var affectedRuns = runMappings
            .Where(rm => rm.StartPosition < endIndex && rm.EndPosition > startIndex)
            .OrderBy(rm => rm.StartPosition)
            .ToList();

        if (affectedRuns.Count == 0) return false;

        // 获取第一个字符所在的Run
        var firstRun = affectedRuns[0];
        int firstCharPosInRun = startIndex - firstRun.StartPosition;
        int lastCharPosInFirstRun = Math.Min(endIndex - firstRun.StartPosition, firstRun.Run.Text?.Length ?? 0);
        
        string firstRunText = firstRun.Run.Text ?? "";
        
        // 按照用户要求的逻辑判断：第一个Run是否只包含匹配文本或其中一部分
        if (firstCharPosInRun == 0 && lastCharPosInFirstRun == firstRunText.Length)
        {
            // 情况1：第一个Run只包含匹配文本的全部或部分字符，不包含其他字符
            if (affectedRuns.Count == 1)
            {
                // 只有一个Run被影响，直接替换
                firstRun.Run.Text = replaceText;
            }
            else
            {
                // 有多个Run被影响，第一个Run改为替换文本，后续的Run按规则处理
                firstRun.Run.Text = replaceText;
                ProcessSubsequentRuns(affectedRuns.Skip(1).ToList(), startIndex, endIndex);
            }
        }
        else
        {
            // 情况2：第一个Run还包含其他字符，需要分割
            SplitFirstRunAndReplace(firstRun, firstCharPosInRun, lastCharPosInFirstRun, replaceText);
            
            // 如果还有后续的Run，按规则处理
            if (affectedRuns.Count > 1)
            {
                ProcessSubsequentRuns(affectedRuns.Skip(1).ToList(), startIndex, endIndex);
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// 分割第一个Run并进行替换，参考SetPunctuationStep的分割逻辑
    /// </summary>
    private void SplitFirstRunAndReplace(RunMapping firstRunMapping, int splitStart, int splitEnd, string replaceText)
    {
        Run originalRun = firstRunMapping.Run;
        string originalText = originalRun.Text ?? "";
        CompositeNode parent = originalRun.ParentNode;
        
        // 分割文本为三部分：前置、替换、后置
        string beforeText = splitStart > 0 ? originalText.Substring(0, splitStart) : "";
        string afterText = splitEnd < originalText.Length ? originalText.Substring(splitEnd) : "";
        
        // 处理前置文本（如果有）
        if (!string.IsNullOrEmpty(beforeText))
        {
            Run beforeRun = CloneRun(originalRun, beforeText);
            parent.InsertBefore(beforeRun, originalRun);
        }
        
        // 处理替换文本
        Run replaceRun = CloneRun(originalRun, replaceText);
        parent.InsertBefore(replaceRun, originalRun);
        
        // 处理后置文本（如果有）
        if (!string.IsNullOrEmpty(afterText))
        {
            Run afterRun = CloneRun(originalRun, afterText);
            parent.InsertBefore(afterRun, originalRun);
        }
        
        // 移除原始Run
        originalRun.Remove();
    }
    
    /// <summary>
    /// 处理后续的Run，按照用户要求的逻辑
    /// </summary>
    private void ProcessSubsequentRuns(List<RunMapping> subsequentRuns, int globalStartIndex, int globalEndIndex)
    {
        foreach (var runMapping in subsequentRuns)
        {
            // 计算在当前Run中的相对位置
            int runStart = Math.Max(0, globalStartIndex - runMapping.StartPosition);
            int runEnd = Math.Min(runMapping.Run.Text?.Length ?? 0, globalEndIndex - runMapping.StartPosition);
            
            if (runStart >= runEnd) continue;
            
            string runText = runMapping.Run.Text ?? "";
            
            if (runStart == 0 && runEnd == runText.Length)
            {
                // 情况1：后续文本占据整个Run，直接删除这个Run
                runMapping.Run.Remove();
            }
            else
            {
                // 情况2：后续文本所在的Run还包含其他文本，只删除匹配的部分
                string beforePart = runStart > 0 ? runText.Substring(0, runStart) : "";
                string afterPart = runEnd < runText.Length ? runText.Substring(runEnd) : "";
                runMapping.Run.Text = beforePart + afterPart;
            }
        }
    }
    
    /// <summary>
    /// 构建Run映射关系，参考NormalizeMathFunctionStep的实现
    /// </summary>
    private List<RunMapping> BuildRunMappings(List<Run> runs)
    {
        var runMappings = new List<RunMapping>();
        int currentPosition = 0;

        foreach (var run in runs)
        {
            string runText = run.Text ?? "";
            runMappings.Add(new RunMapping
            {
                Run = run,
                StartPosition = currentPosition,
                EndPosition = currentPosition + runText.Length,
                Text = runText
            });
            currentPosition += runText.Length;
        }

        return runMappings;
    }
    
    /// <summary>
    /// 查找文本中所有匹配位置
    /// </summary>
    private List<(int start, int end)> FindAllMatches(string text, string target)
    {
        var matches = new List<(int, int)>();
        int index = 0;

        while (index < text.Length)
        {
            int foundIndex = text.IndexOf(target, index, StringComparison.Ordinal);
            if (foundIndex == -1) break;

            matches.Add((foundIndex, foundIndex + target.Length));
            index = foundIndex + target.Length;
        }

        return matches;
    }
    
    /// <summary>
    /// 克隆Run并设置新文本，参考SetPunctuationStep的实现
    /// </summary>
    private Run CloneRun(Run sourceRun, string newText)
    {
        Run clonedRun = (Run)sourceRun.Clone(true);
        clonedRun.Text = newText;
        return clonedRun;
    }

    /// <summary>
    /// 检查Run节点是否在OMML公式中
    /// </summary>
    private bool IsInOfficeMath(Run run)
    {
        Node current = run.ParentNode;
        while (current != null)
        {
            if (current.NodeType == NodeType.OfficeMath)
            {
                return true;
            }
            current = current.ParentNode;
        }
        return false;
    }
    
    /// <summary>
    /// 内部类：Run映射信息，参考NormalizeMathFunctionStep
    /// </summary>
    private class RunMapping
    {
        public Run Run { get; set; } = null!;
        public int StartPosition { get; set; }
        public int EndPosition { get; set; }
        public string Text { get; set; } = string.Empty;
    }
    
    /// <summary>
    /// 段落格式信息保存和恢复类
    /// </summary>
    private class ParagraphFormatInfo
    {
        public ParagraphAlignment Alignment { get; }
        public double LeftIndent { get; }
        public double RightIndent { get; }
        public double FirstLineIndent { get; }
        public double SpaceBefore { get; }
        public double SpaceAfter { get; }
        public double LineSpacing { get; }
        public LineSpacingRule LineSpacingRule { get; }
        public bool KeepTogether { get; }
        public bool KeepWithNext { get; }
        public bool PageBreakBefore { get; }
        public bool WidowControl { get; }
        
        public ParagraphFormatInfo(ParagraphFormat format)
        {
            Alignment = format.Alignment;
            LeftIndent = format.LeftIndent;
            RightIndent = format.RightIndent;
            FirstLineIndent = format.FirstLineIndent;
            SpaceBefore = format.SpaceBefore;
            SpaceAfter = format.SpaceAfter;
            LineSpacing = format.LineSpacing;
            LineSpacingRule = format.LineSpacingRule;
            KeepTogether = format.KeepTogether;
            KeepWithNext = format.KeepWithNext;
            PageBreakBefore = format.PageBreakBefore;
            WidowControl = format.WidowControl;
        }
        
        public void ApplyTo(ParagraphFormat format)
        {
            format.Alignment = Alignment;
            format.LeftIndent = LeftIndent;
            format.RightIndent = RightIndent;
            format.FirstLineIndent = FirstLineIndent;
            format.SpaceBefore = SpaceBefore;
            format.SpaceAfter = SpaceAfter;
            format.LineSpacing = LineSpacing;
            format.LineSpacingRule = LineSpacingRule;
            format.KeepTogether = KeepTogether;
            format.KeepWithNext = KeepWithNext;
            format.PageBreakBefore = PageBreakBefore;
            format.WidowControl = WidowControl;
        }
    }
    
    /// <summary>
    /// Run字体格式信息保存和恢复类
    /// </summary>
    private class RunFormatInfo
    {
        public string Name { get; }
        public string NameFarEast { get; }
        public double Size { get; }
        public bool Bold { get; }
        public bool Italic { get; }
        public Underline Underline { get; }
        public Color Color { get; }
        public bool Hidden { get; }
        public bool SmallCaps { get; }
        public bool AllCaps { get; }
        
        public RunFormatInfo(Font font)
        {
            Name = font.Name;
            NameFarEast = font.NameFarEast;
            Size = font.Size;
            Bold = font.Bold;
            Italic = font.Italic;
            Underline = font.Underline;
            Color = font.Color;
            Hidden = font.Hidden;
            SmallCaps = font.SmallCaps;
            AllCaps = font.AllCaps;
        }
        
        public void ApplyTo(Font font)
        {
            font.Name = Name;
            font.NameFarEast = NameFarEast;
            font.Size = Size;
            font.Bold = Bold;
            font.Italic = Italic;
            font.Underline = Underline;
            font.Color = Color;
            font.Hidden = Hidden;
            font.SmallCaps = SmallCaps;
            font.AllCaps = AllCaps;
        }
    }

    /// <summary>
    /// 在OMML公式中进行文本替换
    /// </summary>
    /// <param name="doc">文档对象</param>
    /// <returns>替换次数</returns>
    private int ReplaceInOmmlFormulas(Document doc)
    {
        int replacementCount = 0;

        // 获取文档中所有的OMML公式节点
        var officeMathNodes = doc.GetChildNodes(NodeType.OfficeMath, true);

        foreach (OfficeMath officeMath in officeMathNodes)
        {
            // 跳过页眉页脚中的公式
            if (IsInHeaderFooter(officeMath))
            {
                continue;
            }

            // 处理公式中的文本
            replacementCount += ReplaceInOfficeMathNode(officeMath);
        }
        return replacementCount;
    }

    /// <summary>
    /// 在单个OMML公式节点中进行文本替换
    /// </summary>
    /// <param name="officeMath">OMML公式节点</param>
    /// <returns>替换次数</returns>
    private int ReplaceInOfficeMathNode(OfficeMath officeMath)
    {
        int replacementCount = 0;

        // 获取公式中的所有Run节点（包含实际文本）
        var runs = officeMath.GetChildNodes(NodeType.Run, true).Cast<Run>().ToList();

        foreach (Run run in runs)
        {
            string originalText = run.Text;
            string modifiedText = originalText;

            // 对每个替换规则进行处理
            foreach (var kvp in _replaceDict)
            {
                if (modifiedText.Contains(kvp.Key))
                {
                    modifiedText = modifiedText.Replace(kvp.Key, kvp.Value);
                    replacementCount++;
                }
            }

            // 如果文本有变化，更新Run节点的文本
            if (modifiedText != originalText)
            {
                run.Text = modifiedText;
            }
        }
        return replacementCount;
    }

    /// <summary>
    /// 检查节点是否在页眉或页脚中
    /// </summary>
    /// <param name="node">要检查的节点</param>
    /// <returns>如果在页眉或页脚中返回true，否则返回false</returns>
    private bool IsInHeaderFooter(Node node)
    {
        // 向上遍历节点树，查找是否有HeaderFooter类型的祖先节点
        Node current = node;
        while (current != null)
        {
            if (current.NodeType == NodeType.HeaderFooter)
            {
                return true;
            }
            current = current.ParentNode;
        }
        return false;
    }
    
    
    
    
    
    // private readonly Dictionary<string, string> _replaceDict;
    //
    // // 构造函数：初始化替换字典
    // // 替换规则字典
    // // - Key：要查找的文本内容
    // // - Value：要替换的目标文本
    // public ReplaceTextStep(Dictionary<string, string> replaceDict)
    // {
    //     // 空值检查确保字典有效性
    //     _replaceDict = replaceDict;
    //     
    //     // 确保至少有一个替换规则
    //     // if (_replaceDict.Count == 0)
    //     //     throw new ArgumentException("替换字典必须包含至少一个替换规则");
    // }
    //
    // // 执行文档内容替换操作
    // public bool Execute(Document doc, string filePath)
    // {
    //     try
    //     {
    //         // 配置替换选项（禁用高级功能）
    //         FindReplaceOptions options = new FindReplaceOptions
    //         {
    //             // MatchCase 属性：是否区分字母大小写
    //             // - true：严格匹配大小写（例如 "Apple" 不匹配 "apple"）
    //             // - false：忽略大小写（默认值）
    //             MatchCase = true,
    //             
    //             // FindWholeWordsOnly 属性：是否仅匹配完整单词
    //             // - true：只匹配独立单词（例如 "cat" 不匹配 "category" 中的 "cat" 部分）
    //             // - false：允许部分匹配（默认值）
    //             FindWholeWordsOnly = false,
    //             
    //             // UseSubstitutions 属性：是否启用正则表达式替换组解析
    //             // - true：允许替换字符串中使用正则表达式捕获组（如 "$1" 表示第一个捕获组）
    //             // - false：替换字符串中的特殊符号（如 "$"）将被视为普通字符（默认值）
    //             UseSubstitutions = false,
    //         };
    //
    //         // 遍历字典执行所有替换规则
    //         foreach (KeyValuePair<string, string> pair in _replaceDict)
    //         {
    //             try
    //             {
    //                 doc.Range.Replace(
    //                     pattern: pair.Key,
    //                     replacement: pair.Value, 
    //                     options: options);
    //             }
    //             catch (Exception)
    //             {
    //                 return false;
    //             }
    //         }
    //         return true;
    //     }
    //     catch (Exception)
    //     {
    //         return false;
    //     }
    // }
}