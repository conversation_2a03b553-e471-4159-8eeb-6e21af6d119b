using System.Drawing;
using System.Text.RegularExpressions;
using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

/// <summary>
/// 指定字段格式设置步骤（实现IPipelineStep接口）
/// 功能：对文档中的指定字段进行严格匹配并设置字体格式
/// </summary>
public sealed class FormatSpecificTextStep : IPipelineStep
{
    // 字段声明
    private readonly string? _targetText;      // 要匹配的目标字段
    private readonly string? _latinFont;       // 西文字体配置（null表示不替换）
    private readonly string? _cjkFont;         // 中文字体配置（null表示不替换）
    private readonly string? _fontSize;        // 字号配置（null表示不替换）
    private readonly Color? _fontColor;        // 字体颜色配置（null表示不替换）
    private readonly bool? _isItalic;          // 斜体配置（null表示不替换）
    private readonly bool? _isUnderline;       // 下划线配置（null表示不替换）

    /// <summary>
    /// 构造函数：初始化格式配置
    /// </summary>
    /// <param name="targetText">要匹配的指定字段，null表示跳过该步骤</param>
    /// <param name="latinFont">西文字体名称，null表示不改变</param>
    /// <param name="cjkFont">中文字体名称，null表示不改变</param>
    /// <param name="fontSize">字号（中文字号或磅值），null表示不改变</param>
    /// <param name="fontColor">字体颜色，null表示不改变</param>
    /// <param name="isItalic">是否设置斜体，null表示不改变</param>
    /// <param name="isUnderline">是否设置下划线，null表示不改变</param>
    public FormatSpecificTextStep(string? targetText, string? latinFont, string? cjkFont,
                                 string? fontSize, Color? fontColor, bool? isItalic, bool? isUnderline = null)
    {
        _targetText = targetText;
        _latinFont = string.IsNullOrWhiteSpace(latinFont) ? null : latinFont;
        _cjkFont = string.IsNullOrWhiteSpace(cjkFont) ? null : cjkFont;
        _fontSize = string.IsNullOrWhiteSpace(fontSize) ? null : fontSize;
        _fontColor = fontColor;
        _isItalic = isItalic;
        _isUnderline = isUnderline;
    }

    /// <summary>
    /// 主执行方法：文档处理入口
    /// </summary>
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 如果目标字段为空，跳过处理
            if (string.IsNullOrEmpty(_targetText))
            {
                return true;
            }

            // 获取所有段落节点
            NodeCollection paragraphs = doc.GetChildNodes(NodeType.Paragraph, true);

            // 遍历处理每个段落
            foreach (Paragraph paragraph in paragraphs)
            {
                // 跳过页眉页脚内容
                if (IsInHeaderFooter(paragraph)) continue;

                // 处理段落中的目标字段
                ProcessParagraph(paragraph);
            }

            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    /// <summary>
    /// 检查节点是否位于页眉/页脚区域
    /// </summary>
    private static bool IsInHeaderFooter(Node node)
    {
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    /// <summary>
    /// 检查节点是否位于微软公式中
    /// </summary>
    private static bool IsInOfficeMath(Node node)
    {
        return node.GetAncestor(typeof(OfficeMath)) != null;
    }

    /// <summary>
    /// 处理段落中的目标字段
    /// </summary>
    private void ProcessParagraph(Paragraph paragraph)
    {
        // 获取段落中所有的Run节点
        NodeCollection runs = paragraph.GetChildNodes(NodeType.Run, true);
        if (runs.Count == 0) return;

        // 构建完整的段落文本和Run映射
        var textBuilder = new System.Text.StringBuilder();
        var runMap = new List<(int start, int end, Run run)>();

        foreach (Run run in runs)
        {
            // 跳过公式中的Run
            if (IsInOfficeMath(run)) continue;

            int start = textBuilder.Length;
            textBuilder.Append(run.Text);
            int end = textBuilder.Length;
            runMap.Add((start, end, run));
        }

        string fullText = textBuilder.ToString();

        // 查找目标字段的所有出现位置（严格匹配）
        var matches = FindExactMatches(fullText, _targetText);

        // 对每个匹配的字段进行格式设置
        foreach (var (startIndex, endIndex) in matches)
        {
            ApplyFormatToTextRange(runMap, startIndex, endIndex);
        }
    }

    /// <summary>
    /// 查找文本中的精确匹配位置
    /// </summary>
    private List<(int start, int end)> FindExactMatches(string text, string target)
    {
        var matches = new List<(int, int)>();
        int index = 0;

        while (index < text.Length)
        {
            int foundIndex = text.IndexOf(target, index, StringComparison.Ordinal);
            if (foundIndex == -1) break;

            matches.Add((foundIndex, foundIndex + target.Length));
            index = foundIndex + target.Length;
        }

        return matches;
    }

    /// <summary>
    /// 对指定文本范围应用格式设置
    /// </summary>
    private void ApplyFormatToTextRange(List<(int start, int end, Run run)> runMap, 
                                       int targetStart, int targetEnd)
    {
        var affectedRuns = new List<(Run run, int runStart, int runEnd)>();

        // 找到受影响的Run
        foreach (var (start, end, run) in runMap)
        {
            // 计算重叠区域
            int overlapStart = Math.Max(start, targetStart);
            int overlapEnd = Math.Min(end, targetEnd);

            if (overlapStart < overlapEnd)
            {
                affectedRuns.Add((run, overlapStart - start, overlapEnd - start));
            }
        }

        // 对受影响的Run进行处理
        foreach (var (run, runStart, runEnd) in affectedRuns)
        {
            if (runStart == 0 && runEnd == run.Text.Length)
            {
                // 整个Run都需要格式化，直接应用格式
                ApplyFontSettings(run);
            }
            else
            {
                // 需要拆分Run
                SplitRunAndApplyFormat(run, runStart, runEnd);
            }
        }
    }

    /// <summary>
    /// 拆分Run并对指定部分应用格式
    /// </summary>
    private void SplitRunAndApplyFormat(Run originalRun, int startIndex, int endIndex)
    {
        string originalText = originalRun.Text;
        string beforeText = originalText.Substring(0, startIndex);
        string targetText = originalText.Substring(startIndex, endIndex - startIndex);
        string afterText = originalText.Substring(endIndex);

        CompositeNode parent = originalRun.ParentNode;

        // 创建前置文本Run（如果有）
        if (!string.IsNullOrEmpty(beforeText))
        {
            Run beforeRun = CloneRun(originalRun, beforeText);
            parent.InsertBefore(beforeRun, originalRun);
        }

        // 创建目标文本Run并应用格式
        Run targetRun = CloneRun(originalRun, targetText);
        ApplyFontSettings(targetRun);
        parent.InsertBefore(targetRun, originalRun);

        // 创建后置文本Run（如果有）
        if (!string.IsNullOrEmpty(afterText))
        {
            Run afterRun = CloneRun(originalRun, afterText);
            parent.InsertBefore(afterRun, originalRun);
        }

        // 移除原始Run
        originalRun.Remove();
    }

    /// <summary>
    /// 克隆Run并设置新文本
    /// </summary>
    private Run CloneRun(Run originalRun, string newText)
    {
        Run newRun = (Run)originalRun.Clone(true);
        newRun.Text = newText;
        return newRun;
    }

    /// <summary>
    /// 应用字体设置到Run
    /// </summary>
    private void ApplyFontSettings(Run run)
    {
        // 西文字体替换
        if (_latinFont != null)
        {
            run.Font.Name = _latinFont;
        }

        // 中文字体替换
        if (_cjkFont != null)
        {
            run.Font.NameFarEast = _cjkFont;
        }

        // 字号设置
        if (_fontSize != null)
        {
            try
            {
                // 尝试解析为数字（磅值）
                if (double.TryParse(_fontSize, out double pointSize))
                {
                    run.Font.Size = pointSize;
                }
                else
                {
                    // 尝试作为中文字号转换
                    run.Font.Size = FontSizeConverter.GetPointSize(_fontSize);
                }
            }
            catch (ArgumentException)
            {
                // 无效字号，忽略
            }
        }

        // 颜色设置
        if (_fontColor.HasValue)
        {
            run.Font.Color = _fontColor.Value;
        }

        // 斜体设置
        if (_isItalic.HasValue)
        {
            run.Font.Italic = _isItalic.Value;
        }

        // 下划线设置
        if (_isUnderline.HasValue)
        {
            run.Font.Underline = _isUnderline.Value ? Underline.Single : Underline.None;
        }
    }
} 