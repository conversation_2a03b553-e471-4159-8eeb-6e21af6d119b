using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.Interfaces;
using WordProcessorLib.Utilities;

namespace WordProcessorLib.PipelineSteps;

// 字体替换处理步骤（实现IPipelineStep接口）
// 功能：通过构造函数接收中西文字体配置，独立替换文档字体
public class NormalizeMathStep : IPipelineStep
{
    // 字段声明
    private readonly string _mathLatinFont;         // 西文字体配置（null表示不替换）
    private readonly string _mathLatinFontSize;     // 西文字号配置（null表示不替换）
    private readonly string _mathCjkFont;           // 中文字体配置（null表示不替换）
    private readonly string _mathCjkFontSize;       // 中文字号配置（null表示不替换）

    // 构造函数：初始化字体配置
    // mathLatinFont：    公式中的西文字体名称，因为要分拆Run，所以不能为空。替换公式中的西文和双向文本字体
    // mathLatinFontSize：公式中的西文字体大小，null或空字符串则保留原公式字体
    // mathCjkFont：      公式中的中文字体名称，因为要分拆Run，所以不能为空。替换公式中的中文字体
    // mathCjkFontSize：  公式中的中文字体大小，null或空字符串则保留原字体
    public NormalizeMathStep(string? mathLatinFont, string? mathLatinFontSize,
                             string? mathCjkFont  , string? mathCjkFontSize)
    {
        _mathLatinFont     = mathLatinFont;
        _mathLatinFontSize = string.IsNullOrWhiteSpace(_mathLatinFontSize) ? null : mathLatinFontSize;
        _mathCjkFont       = mathCjkFont;
        _mathCjkFontSize   = string.IsNullOrWhiteSpace(_mathCjkFontSize) ? null : mathCjkFontSize;
    }

    // 主执行方法：文档处理入口
    public bool Execute(Document doc, string filePath)
    {
        try
        {
            // 获取文档所有节点（深度递归遍历）
            NodeCollection nodes = doc.GetChildNodes(NodeType.Any, true);

            // 遍历处理每个节点
            foreach (Node node in nodes)
            {
                // 跳过页眉页脚内容
                if (IsInHeaderFooter(node)) continue;

                // 根据节点类型分发处理
                ProcessNode(node);
            }
            return true;
        }
        catch (Exception)
        {
            return false;
        }
    }

    // 检查节点是否位于页眉/页脚区域
    // node：当前文档节点
    // true：属于页眉页脚内容
    // false：属于正文内容
    private static bool IsInHeaderFooter(Node node)
    {
        // 通过查找最近的HeaderFooter祖先节点判断位置
        return node.GetAncestor(typeof(HeaderFooter)) != null;
    }

    // 节点处理路由中心
    // node：当前文档节点
    private void ProcessNode(Node node)
    {
        // 使用类型模式匹配分发处理
        switch (node)
        {
            case OfficeMath mathNode:
                ProcessOfficeMath(mathNode);
                break;
        }
    }
    
    // ========= 公式节点分割设置斜体方法 =========
    // 作用：处理文档中的数学公式节点，对其中的文本按中西文分别设置斜体样式
    // 用法：在遍历文档节点时被调用，传入检测到的OfficeMath类型节点
    // 必要性：实现公式中中西文字体分别处理的核心方法
    private void ProcessOfficeMath(OfficeMath mathNode)
    {
        // 获取公式中所有文本运行节点（深度遍历）
        NodeCollection runs = mathNode.GetChildNodes(NodeType.Run, true);
    
        // 创建需要处理的Run节点列表（避免在遍历过程中修改集合导致的问题）
        // 作用：将待处理的Run节点复制到一个独立的列表中
        // 用法：遍历原节点集合并添加到新列表中
        // 必要性：非常必要，因为后续操作会修改节点结构（添加新节点），在遍历原集合时直接修改会导致集合被修改的异常
        List<Run> runList = new List<Run>();
        foreach (Run run in runs)
        {
            runList.Add(run);
        }
    
        // 遍历处理公式中的每个文本运行
        foreach (Run run in runList)
        {
            // 获取当前文本内容
            // 必要性：需要文本内容才能进行中西文判断和分段
            string text = run.Text;
            
            // 检查文本是否为空，为空则跳过当前循环
            if (string.IsNullOrEmpty(text))
                continue;
            
            // 调用自定义的SplitTextByChinese方法，传入文本内容，将混合文本按中西文分段，返回分段结果，是实现中西文分离的核心逻辑
            List<TextSegment> segments = SplitTextByChinese(text);
            
            // 作用：处理只有一种文字类型的简单情况
            // 用法：检查分段数量，如果只有一段，直接设置斜体属性（中文非斜体，西文斜体）
            // 必要性：优化处理，避免对单一类型文本进行不必要的分拆操作
            if (segments.Count == 1)
            {
                if (segments[0].IsChinese)
                {
                    run.Font.Italic       = false;
                    run.Font.Name         = _mathLatinFont;
                    run.Font.NameFarEast  = _mathCjkFont;
                    if (_mathCjkFontSize != null)
                        run.Font.Size = FontSizeConverter.GetPointSize(_mathCjkFontSize);
                }
                else
                {
                    run.Font.Italic         = true;
                    run.Font.Name           = _mathLatinFont;
                    run.Font.NameFarEast    = _mathCjkFont;
                    if (_mathLatinFontSize != null)
                        run.Font.Size = FontSizeConverter.GetPointSize(_mathLatinFontSize);
                }
                continue;
            }
        
            // 用法：将原Run节点用于第一个文本段，更新原Run的文本内容为第一段文本，并设置适当的斜体属性，此时 run 就变成了拆分后的第一个节点
            // 必要性：重用原节点减少创建新节点的开销，提高效率
            run.Text = segments[0].Text;
            run.Font.Italic = !segments[0].IsChinese;
            
            // 跟踪最后插入的节点
            Run lastInsertedRun = run;
        
            // 遍历处理后续的文本段，从索引1开始的for循环，需要为每个后续段落创建新的Run节点
            for (int i = 1; i < segments.Count; i++)
            {
                // 作用：创建新Run节点并设置其内容和样式
                // 用法：克隆原Run保留其所有属性和格式，设置新文本内容，根据是否为中文设置斜体属性
                // 必要性：确保新创建的Run保持原有格式，只修改文本内容和斜体属性
                Run newRun = (Run)run.Clone(true);
                newRun.Text = segments[i].Text;
                
                if (segments[i].IsChinese)
                {
                    newRun.Font.Italic      = false;
                    newRun.Font.Name        = _mathLatinFont;
                    newRun.Font.NameFarEast = _mathCjkFont;
                    if (_mathCjkFontSize != null)
                        newRun.Font.Size = FontSizeConverter.GetPointSize(_mathCjkFontSize);
                }
                else
                {
                    newRun.Font.Italic         = true;
                    newRun.Font.Name           = _mathLatinFont;
                    newRun.Font.NameFarEast    = _mathCjkFont;
                    if (_mathLatinFontSize != null)
                        newRun.Font.Size = FontSizeConverter.GetPointSize(_mathLatinFontSize);
                }
            
                // 在最后插入的节点后插入新节点
                run.ParentNode.InsertAfter(newRun, lastInsertedRun);
    
                // 更新最后插入的节点
                lastInsertedRun = newRun;
            }
        }
    }
    
    // 作用：存储分段后的文本信息
    // 用法：简单的数据类，包含文本内容和是否为中文的标志
    // 必要性：封装文本段信息，使代码更清晰易读
    private class TextSegment
    {
        public string Text { get; set; }      // 段落文本内容
        public bool IsChinese { get; set; }   // 是否为中文段落
    }

    // 作用：将混合文本按中西文类型分段
    // 用法：传入文本字符串，返回分段后的TextSegment列表
    // 必要性：实现中西文分离的核心算法
    private List<TextSegment> SplitTextByChinese(string text)
    {
        // 作用：创建用于存储分段结果的列表
        // 用法：标准列表初始化
        // 必要性：存储分段结果的容器
        List<TextSegment> segments = new List<TextSegment>();
        
        // 当前正在处理的字符类型（初始为未知）
        bool? isCurrentChinese = null;
        // 当前当前段落的起始位置
        // 必要性：必须跟踪字符类型和段落起始位置才能正确分段
        int startIndex = 0;
        
        // 逐字符遍历文本
        for (int i = 0; i < text.Length; i++)
        {
            // 判断当前字符是否为中文
            bool isChinese = IsChineseCharacter(text[i]);
            
            // 如果字符类型发生变化或是最后一个字符，创建新段落
            if (isCurrentChinese != null && (isCurrentChinese != isChinese || i == text.Length - 1))
            {
                // 对于最后一个字符且类型未变化的情况，需调整结束位置
                int endIndex = (i == text.Length - 1 && isCurrentChinese == isChinese) ? i + 1 : i;
                
                // 创建段落并添加到列表
                segments.Add(new TextSegment
                {
                    Text = text.Substring(startIndex, endIndex - startIndex),
                    IsChinese = isCurrentChinese.Value
                });
                
                // 更新下一段的起始位置
                startIndex = i;
            }
            
            // 更新当前字符类型
            isCurrentChinese = isChinese;
        }
        
        // 处理只有一种类型字符的情况
        if (segments.Count == 0 && text.Length > 0)
        {
            segments.Add(new TextSegment
            {
                Text = text,
                IsChinese = IsChineseCharacter(text[0])
            });
        }
        return segments;
    }
    
    // 判断单个字符是否为中文字符
    // c：待检查的字符
    // 返回值：true表示是中文字符，false表示不是
    private bool IsChineseCharacter(char c)
    {
        // 中文字符的Unicode范围：
        // 基本汉字：[\u4e00-\u9fa5]
        // 扩展汉字A：[\u3400-\u4dbf]
        // 检查是否在中文字符范围内，中文字符的Unicode范围：
        return // 基本汉字：[\u4e00-\u9fa5]
            (c >= 0x4e00 && c <= 0x9fa5) ||
            
            // CJK扩展区A：[\u3400-\u4dbf]
            (c >= 0x3400 && c <= 0x4dbf) ||
            
            // CJK扩展区B：[\u20000-\u2a6df]（需要用Unicode码点处理）
            (c >= 0xD840 && c <= 0xD869 && char.IsHighSurrogate(c)) ||
            
            // CJK扩展区C-F也可以用类似方法处理，但需要surrogate pair处理
            
            // 中文标点符号部分
            // 全角标点符号：[\u3000-\u303f]
            (c >= 0x3000 && c <= 0x303f) ||
            
            // CJK兼容符号：[\uf900-\ufaff]
            (c >= 0xf900 && c <= 0xfaff) ||
            
            // 中文全角标点和符号：[\uff00-\uffef]
            (c >= 0xff00 && c <= 0xffef) ||
            
            // 中文竖排标点：[\ufe10-\ufe1f]
            (c >= 0xfe10 && c <= 0xfe1f) ||
            
            // 中文括号和其他符号：[\u3008-\u3020]
            (c >= 0x3008 && c <= 0x3020) ||
            
            // 中文数字：[\u3021-\u3029]
            (c >= 0x3021 && c <= 0x3029) ||
            
            // 中文月份：[\u3220-\u3243]
            (c >= 0x3220 && c <= 0x3243) ||
            
            // 康熙部首：[\u2F00-\u2FD5]
            (c >= 0x2F00 && c <= 0x2FD5)||
        
            // 新增精准匹配
            c == '\u201C' ||        // “
            c == '\u201D' ||        // ”
            c == '\u2018' ||        // ‘
            c == '\u2019' ||        // ’
            c == '\u2014' ||        // —
            c == '\u2026' ||        // …
            c == '\u00B7' ||        // ·
            c == '\uFF08' ||        // （
            c == '\uFF09';          // ）
    }
}