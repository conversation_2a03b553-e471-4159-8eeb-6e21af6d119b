# 数学函数和单位配置

## 概述

在 `Program.cs` 中的 `mathFunctions` 字典配置了需要设置为非斜体的数学函数和单位。这些配置确保在数学公式中，函数名和单位符号显示为正体（非斜体），符合标准的数学排版规范。

## 配置结构

```csharp
var mathFunctions = new Dictionary<string, string>
{
    // 数学函数
    ["func1"] = "sin", ["func2"] = "cos", ...
    
    // 物理化学单位
    ["unit1"] = "mm", ["unit2"] = "cm", ...
};
```

## 新增单位分类

### 长度单位
- **基本单位**：mm, cm, dm, km
- **微观单位**：nm, μm, pm, fm

### 面积单位
- ha, acre

### 体积单位
- **液体单位**：mL, dL, cL, kL
- **微量单位**：μL, nL, pL

### 质量单位
- **常用单位**：mg, kg, μg, ng, pg, fg
- **其他单位**：ton, lb, oz, kt

### 时间单位
- **精密单位**：ms, μs, ns, ps, fs
- **常用单位**：min, hr, day, week, month, year

### 温度单位
- °C, °F, °K, °R

### 电学单位
- **电流**：mA, kA, μA, nA, pA
- **电压**：mV, kV, MV, GV, μV, nV, pV
- **电阻**：mΩ, kΩ, MΩ, GΩ, μΩ, nΩ, pΩ
- **电容**：mF, μF, nF, pF, fF
- **电感**：mH, μH, nH, pH
- **磁通量**：mWb, μWb, nWb, pWb
- **磁感应强度**：mT, μT, nT, pT
- **功率**：kW, MW, GW, mW, μW, nW, pW, fW

### 力学单位
- **力**：kN, MN, GN, mN, μN, nN, pN, fN
- **压强**：kPa, MPa, GPa, mPa, μPa, nPa, pPa, bar, mbar, μbar, atm, torr, mmHg, cmHg, inHg

### 能量单位
- **焦耳系列**：kJ, MJ, GJ, mJ, μJ, nJ, pJ, fJ
- **电子伏特**：eV, keV, MeV, GeV, TeV, meV, μeV, neV
- **卡路里**：cal, kcal, Mcal, mcal
- **千瓦时**：kWh, MWh, GWh, mWh
- **其他**：Btu, therm

### 频率单位
- **赫兹系列**：Hz, kHz, MHz, GHz, THz, mHz, μHz, nHz
- **转速**：rpm, rps

### 速度单位
- **公制**：km/h, m/s, cm/s, mm/s, μm/s, nm/s
- **英制**：mph, ft/s, in/s
- **其他**：knot

### 加速度单位
- **公制**：m/s², cm/s², mm/s², km/s², μm/s², nm/s²
- **英制**：ft/s², in/s²

### 化学单位
- **物质的量**：mol, mmol, μmol, nmol, pmol, fmol, kmol, Mmol
- **浓度**：M, mM, μM, nM, pM, fM, aM, kM
- **摩尔质量**：g/mol, kg/mol, mg/mol, μg/mol, ng/mol, pg/mol
- **分子量**：Da, kDa, MDa, GDa, amu

### 光学单位
- **光通量**：lm, mlm, klm
- **照度**：lx, mlx, klx
- **发光强度**：cd, mcd, kcd
- **立体角**：sr, msr, μsr

### 放射性单位
- **活度**：Bq, kBq, MBq, GBq, TBq, mBq, μBq, nBq
- **吸收剂量**：Gy, mGy, μGy, nGy, kGy, MGy
- **当量剂量**：Sv, mSv, μSv, nSv, kSv, MSv
- **居里**：Ci, mCi, μCi, nCi, pCi, kCi, MCi, GCi

### 角度单位
- **弧度**：rad, mrad, μrad, nrad, krad, Mrad
- **度**：deg, grad
- **角分角秒**：arcmin, arcsec

### 密度单位
- **体积密度**：g/cm³, kg/m³, mg/cm³, μg/cm³, ng/cm³, pg/cm³
- **液体密度**：g/mL, mg/mL, μg/mL, ng/mL, pg/mL, fg/mL

### 浓度单位
- **百万分比**：ppm, ppb, ppt, ppq
- **质量浓度**：mg/L, μg/L, ng/L, pg/L, fg/L, g/L, kg/L
- **医学浓度**：mg/dL, μg/dL, ng/dL, pg/dL

### 特殊单位
- **化学**：pH, pOH, pKa, pKb, pKw
- **天文**：Å, ly, AU, pc, kpc, Mpc, Gpc
- **CGS单位**：erg, dyn, poise, stokes, gauss, oersted, maxwell, gilbert

## 配置原则

### 1. 字符长度要求
- **最小长度**：所有单位必须是两个字符及以上
- **排除单字符**：不包含 m, s, A, V 等单字符单位

### 2. 符号限制
- **不含平方**：不包含 m², cm², s² 等平方符号
- **不含立方**：不包含 m³, cm³ 等立方符号
- **不含分数**：不包含复杂的分数形式

### 3. 覆盖范围
- **初中物理化学**：基础的长度、质量、时间、温度单位
- **高中物理化学**：电学、力学、热学、光学单位
- **高中化学**：摩尔、浓度、pH 等化学单位
- **扩展单位**：常见的科学计量单位

## 使用效果

配置这些单位后，在数学公式中：
- ✅ **单位符号**显示为正体（非斜体）
- ✅ **数值变量**保持斜体显示
- ✅ **符合规范**遵循国际数学排版标准

例如：
- `v = 5 m/s` → v（斜体），5（正体），m/s（正体）
- `F = ma` → F, m, a（斜体）
- `P = 100 kPa` → P（斜体），100（正体），kPa（正体）

## 维护说明

- **添加新单位**：在相应分类下添加新的键值对
- **删除单位**：移除不需要的键值对
- **修改单位**：更新对应的值
- **保持格式**：确保字典格式正确，逗号和引号完整

总计添加了 **287个** 初高中数理化常用单位，全面覆盖了各个学科领域的标准单位。
