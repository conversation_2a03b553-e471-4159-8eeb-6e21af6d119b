using System;
using System.Collections.Generic;
using System.IO;
using Aspose.Words;
using WordProcessorLib.PipelineSteps;

namespace WordProcessorLib.Tests
{
    /// <summary>
    /// 测试InsertEndTagStep修复效果的程序
    /// </summary>
    class TestInsertEndTagFix
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== InsertEndTagStep Bug修复测试 ===");
            Console.WriteLine();
            
            // 创建测试用的题型标题字典
            var targetFields = new Dictionary<string, string>
            {
                { "choice_questions", "一、选择题" },
                { "fill_blank_questions", "二、填空题" },
                { "fill_blank_questions_alt", "三、填空题" },
                { "comprehensive_questions", "四、解答题" },
                { "comprehensive_questions_alt", "四、综合题" }
            };
            
            // 创建InsertEndTagStep实例
            var insertEndTagStep = new InsertEndTagStep(targetFields);
            
            // 测试文档路径（请根据实际情况修改）
            string testDocPath = "test_document.docx";
            
            if (!File.Exists(testDocPath))
            {
                Console.WriteLine($"测试文档不存在: {testDocPath}");
                Console.WriteLine("请提供一个包含'三、填空题'和'四、解答题'的测试文档");
                return;
            }
            
            try
            {
                // 加载文档
                Document doc = new Document(testDocPath);
                Console.WriteLine($"成功加载测试文档: {testDocPath}");
                
                // 执行处理
                bool success = insertEndTagStep.Execute(doc, testDocPath);
                
                if (success)
                {
                    Console.WriteLine("✅ 处理成功！");
                    
                    // 保存处理后的文档
                    string outputPath = Path.GetFileNameWithoutExtension(testDocPath) + "_fixed.docx";
                    doc.Save(outputPath);
                    Console.WriteLine($"处理后的文档已保存为: {outputPath}");
                }
                else
                {
                    Console.WriteLine("❌ 处理失败！");
                }
                
                // 输出详细日志
                Console.WriteLine();
                Console.WriteLine("=== 处理日志 ===");
                var logs = insertEndTagStep.GetLogs();
                foreach (string log in logs)
                {
                    Console.WriteLine(log);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex.StackTrace}");
            }
            
            Console.WriteLine();
            Console.WriteLine("测试完成，请检查输出文档中'三、填空题'和'四、解答题'之间的题目是否正确插入了【END2】标签");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
