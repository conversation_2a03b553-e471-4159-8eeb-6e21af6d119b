﻿using System;
using System.Text.RegularExpressions;

// 测试 RemoveQuestionSourceStep 的 Unicode 空格处理功能
class Program
{
    static void Main()
    {
        // 模拟 RemoveQuestionSourceStep 中的正则表达式
        Regex sourcePattern = new Regex(@"^\s*（[^（）]*）\s*$", RegexOptions.Compiled);
        
        // 测试各种空格字符的支持
        var testCases = new[]
        {
            "（2024年高考题）",                 // 标准格式
            " （2024年高考题）",                // 普通空格前缀
            "　（2024年高考题）",                // 全角空格前缀
            "\u00A0（2024年高考题）",           // 不间断空格
            "\u2003（2024年高考题）",           // em空格
            "\u2002（2024年高考题）",           // en空格
            "\u2009（2024年高考题）",           // 细空格
            "\u200B（2024年高考题）",           // 零宽空格
            "  （2024年高考题）  ",             // 前后都有普通空格
            "　　（2024年高考题）　　",           // 前后都有全角空格
            "\t（2024年高考题）\t",             // 制表符
            " \u00A0\u2003（2024年高考题）\u2002\u2009 ", // 混合空格
            "文字（2024年高考题）",              // 前面有文字（应该不匹配）
            "（2024年高考题）文字",              // 后面有文字（应该不匹配）
            "（）",                           // 空括号
            " （） ",                         // 空括号带空格
        };
        
        Console.WriteLine("测试 Unicode 空格字符对题目来源识别的支持：");
        Console.WriteLine("=".PadRight(60, '='));
        
        foreach (var testCase in testCases)
        {
            bool matches = sourcePattern.IsMatch(testCase);
            string displayText = testCase.Replace("\u00A0", "[NBSP]")
                                        .Replace("\u2003", "[EMSP]")
                                        .Replace("\u2002", "[ENSP]") 
                                        .Replace("\u2009", "[THINSP]")
                                        .Replace("\u200B", "[ZWSP]")
                                        .Replace("\t", "[TAB]")
                                        .Replace("　", "[FWSP]");
            
            Console.WriteLine($"{(matches ? "✓" : "✗")} \"{displayText}\"");
        }
        
        // 额外测试：RemoveAllWhitespaces 方法
        Console.WriteLine("\n测试空格移除功能：");
        Console.WriteLine("=".PadRight(40, '='));
        
        string mixedSpaceText = " \u00A0\u2003测试\u2002文字\u2009 ";
        string cleanText = Regex.Replace(mixedSpaceText, @"\s", "", RegexOptions.Compiled);
        
        Console.WriteLine($"原文: \"{mixedSpaceText.Replace("\u00A0", "[NBSP]").Replace("\u2003", "[EMSP]").Replace("\u2002", "[ENSP]").Replace("\u2009", "[THINSP]")}\"");
        Console.WriteLine($"清理后: \"{cleanText}\"");
    }
}
