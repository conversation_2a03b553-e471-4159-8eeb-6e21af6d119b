using System;
using System.Collections.Generic;
using System.IO;
using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.PipelineSteps;

namespace WordProcessorLib.Tests
{
    /// <summary>
    /// 测试NormalizeMathFunctionStep跨Run匹配功能的程序
    /// 验证按照用户设想实现的功能：
    /// 1. 支持跨Run的函数匹配（函数名可以分布在多个Run中）
    /// 2. 支持部分匹配（如asinnx中的sin）
    /// 3. 保持Run结构不合并，只修改斜体属性
    /// </summary>
    class TestNormalizeMathFunctionCrossRun
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== NormalizeMathFunctionStep 跨Run匹配功能测试 ===");
            Console.WriteLine();
            
            // 创建测试用的数学函数字典
            var mathFunctions = new Dictionary<string, string>
            {
                { "sin_func", "sin" },
                { "cos_func", "cos" },
                { "tan_func", "tan" },
                { "log_func", "log" },
                { "ln_func", "ln" },
                { "min_func", "min" },
                { "max_func", "max" }
            };
            
            // 创建NormalizeMathFunctionStep实例
            var normalizeMathFunctionStep = new NormalizeMathFunctionStep(mathFunctions);
            
            // 测试文档路径
            string testDocPath = "/Users/<USER>/Downloads/临时作业/临时作业10/【题库】高中数学同步试题必修1  综合试题①  较难.docx";
            
            if (!File.Exists(testDocPath))
            {
                Console.WriteLine($"❌ 测试文档不存在: {testDocPath}");
                Console.WriteLine("请确保测试文档存在后重新运行测试。");
                return;
            }
            
            try
            {
                // 加载文档
                Document doc = new Document(testDocPath);
                Console.WriteLine($"成功加载测试文档: {testDocPath}");
                
                // 分析处理前的公式状态
                Console.WriteLine("\n=== 处理前的公式分析 ===");
                AnalyzeMathFormulas(doc, "处理前");
                
                // 执行处理
                bool success = normalizeMathFunctionStep.Execute(doc, testDocPath);
                
                if (success)
                {
                    Console.WriteLine("\n✅ 处理成功！");
                    
                    // 分析处理后的公式状态
                    Console.WriteLine("\n=== 处理后的公式分析 ===");
                    AnalyzeMathFormulas(doc, "处理后");
                    
                    // 保存处理后的文档
                    string outputPath = Path.GetFileNameWithoutExtension(testDocPath) + "_math_function_test.docx";
                    doc.Save(outputPath);
                    Console.WriteLine($"\n处理后的文档已保存为: {outputPath}");
                }
                else
                {
                    Console.WriteLine("\n❌ 处理失败！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
        
        /// <summary>
        /// 分析文档中的数学公式状态
        /// </summary>
        /// <param name="doc">文档对象</param>
        /// <param name="stage">分析阶段（处理前/处理后）</param>
        private static void AnalyzeMathFormulas(Document doc, string stage)
        {
            // 获取所有数学公式节点
            NodeCollection mathNodes = doc.GetChildNodes(NodeType.OfficeMath, true);
            
            Console.WriteLine($"{stage}共找到 {mathNodes.Count} 个数学公式");
            
            int formulaIndex = 0;
            foreach (OfficeMath mathNode in mathNodes)
            {
                formulaIndex++;
                string formulaText = mathNode.ToString(SaveFormat.Text).Trim();
                
                // 只分析包含目标函数的公式
                if (ContainsTargetFunctions(formulaText))
                {
                    Console.WriteLine($"\n--- 公式 {formulaIndex}: {formulaText} ---");
                    
                    // 分析Run结构
                    NodeCollection runs = mathNode.GetChildNodes(NodeType.Run, true);
                    Console.WriteLine($"Run数量: {runs.Count}");
                    
                    for (int i = 0; i < runs.Count; i++)
                    {
                        Run run = (Run)runs[i];
                        string runText = run.Text ?? "";
                        if (!string.IsNullOrEmpty(runText))
                        {
                            Console.WriteLine($"  Run[{i}]: \"{runText}\" (斜体: {run.Font.Italic})");
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// 检查文本是否包含目标函数
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>是否包含目标函数</returns>
        private static bool ContainsTargetFunctions(string text)
        {
            string[] targetFunctions = { "sin", "cos", "tan", "log", "ln", "min", "max" };
            
            foreach (string func in targetFunctions)
            {
                if (text.Contains(func))
                {
                    return true;
                }
            }
            
            return false;
        }
    }
}
