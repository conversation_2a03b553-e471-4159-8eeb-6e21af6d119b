﻿using System;
using System.Text.RegularExpressions;

/// <summary>
/// 测试【】+题目来源模式的正则表达式
/// </summary>
class Program
{
    static void Main(string[] args)
    {
        // 模式3：【】+题目来源
        Regex bracketPrefixAndSourcePattern = new Regex(@"^\s*【】\s*（[^（）]*）\s*$", RegexOptions.Compiled);
        
        // 测试用例
        string[] testCases = {
            "【】（2023年高考数学）",
            " 【】 （2023年高考数学） ",
            "【】（来源：人教版）",
            "\u3000【】\u3000（全角空格测试）\u3000",  // 全角空格 U+3000
            "\u00A0【】\u00A0（不间断空格测试）\u00A0",  // 不间断空格 U+00A0
            "\t【】\t（制表符测试）\t",
            "【】（）",  // 空括号
            "【】（测试（嵌套）不匹配",  // 包含嵌套括号，应该不匹配
            "其他内容【】（2023年高考数学）",  // 前面有其他内容，应该不匹配
            "【】（2023年高考数学）其他内容",  // 后面有其他内容，应该不匹配
            "【】",  // 只有【】没有题目来源，应该不匹配
            "（2023年高考数学）",  // 只有题目来源没有【】，应该不匹配
        };
        
        Console.WriteLine("测试【】+题目来源模式：");
        Console.WriteLine("====================");
        
        foreach (string testCase in testCases)
        {
            bool isMatch = bracketPrefixAndSourcePattern.IsMatch(testCase);
            string unicodeDisplay = ShowUnicodeChars(testCase);
            Console.WriteLine($"测试: {unicodeDisplay}");
            Console.WriteLine($"匹配: {(isMatch ? "✓" : "✗")}");
            Console.WriteLine();
        }
    }
    
    /// <summary>
    /// 显示字符串中的Unicode字符编码信息
    /// </summary>
    private static string ShowUnicodeChars(string input)
    {
        if (string.IsNullOrEmpty(input))
            return "\"\"";
            
        var result = "\"";
        foreach (char c in input)
        {
            if (char.IsWhiteSpace(c) && c != ' ')
            {
                result += $"\\u{(int)c:X4}";
            }
            else
            {
                result += c;
            }
        }
        result += "\"";
        return result;
    }
}
