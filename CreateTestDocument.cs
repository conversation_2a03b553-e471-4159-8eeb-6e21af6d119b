using System;
using System.IO;
using Aspose.Words;
using Aspose.Words.Math;

namespace CreateTestDocument
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 创建测试文档 ===");
            
            try
            {
                // 创建新文档
                Document doc = new Document();
                DocumentBuilder builder = new DocumentBuilder(doc);
                
                // 添加标题
                builder.Font.Size = 16;
                builder.Font.Bold = true;
                builder.Writeln("数学函数测试文档");
                builder.Font.Bold = false;
                builder.Font.Size = 12;
                builder.Writeln();
                
                // 添加一些包含数学函数的公式
                builder.Writeln("测试公式1：");
                builder.InsertHtml("f(x) = <i>sin</i>x + <i>cos</i>x");
                builder.Writeln();
                builder.Writeln();
                
                builder.Writeln("测试公式2：");
                builder.InsertHtml("g(x) = <i>min</i>{<i>sin</i>ωx, <i>cos</i>ωx}");
                builder.Writeln();
                builder.Writeln();
                
                builder.Writeln("测试公式3：");
                builder.InsertHtml("h(x) = <i>log</i>(<i>tan</i>x) + <i>ln</i>(x)");
                builder.Writeln();
                builder.Writeln();
                
                builder.Writeln("测试公式4（部分匹配）：");
                builder.InsertHtml("k(x) = a<i>sin</i>nx + b<i>cos</i>mx");
                builder.Writeln();
                
                // 保存文档
                string outputPath = "TestMathFunctions.docx";
                doc.Save(outputPath);
                
                Console.WriteLine($"✅ 测试文档创建成功: {outputPath}");
                Console.WriteLine($"完整路径: {Path.GetFullPath(outputPath)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建文档时发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex.StackTrace}");
            }
            
            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
    }
}
