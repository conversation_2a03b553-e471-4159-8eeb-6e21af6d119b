﻿using System;
using System.Collections.Generic;
using System.IO;
using Aspose.Words;
using Aspose.Words.Math;
using WordProcessorLib.PipelineSteps;

namespace TestMathFunction
{
    /// <summary>
    /// 测试NormalizeMathFunctionStep跨Run匹配功能的程序
    /// 验证按照用户设想实现的功能：
    /// 1. 支持跨Run的函数匹配（函数名可以分布在多个Run中）
    /// 2. 支持部分匹配（如asinnx中的sin）
    /// 3. 保持Run结构不合并，只修改斜体属性
    /// </summary>
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== NormalizeMathFunctionStep 跨Run匹配功能测试 ===");
            Console.WriteLine();

            // 创建测试用的数学函数列表
            var mathFunctions = new List<string>
            {
                "sin",
                "cos", 
                "tan",
                "log",
                "ln",
                "min",
                "max"
            };

            // 创建NormalizeMathFunctionStep实例
            var normalizeMathFunctionStep = new NormalizeMathFunctionStep(mathFunctions);

            // 先创建测试文档
            CreateTestDocument();

            // 测试文档路径
            string testDocPath = "TestMathFunctions.docx";

            if (!File.Exists(testDocPath))
            {
                Console.WriteLine($"❌ 测试文档不存在: {testDocPath}");
                Console.WriteLine("请确保测试文档存在后重新运行测试。");
                return;
            }

            try
            {
                // 加载文档
                Document doc = new Document(testDocPath);
                Console.WriteLine($"成功加载测试文档: {testDocPath}");

                // 分析处理前的公式状态
                Console.WriteLine("\n=== 处理前的公式分析 ===");
                AnalyzeMathFormulas(doc, "处理前");

                // 执行处理
                bool success = normalizeMathFunctionStep.Execute(doc, testDocPath);

                if (success)
                {
                    Console.WriteLine("\n✅ 处理成功！");

                    // 分析处理后的公式状态
                    Console.WriteLine("\n=== 处理后的公式分析 ===");
                    AnalyzeMathFormulas(doc, "处理后");

                    // 保存处理后的文档
                    string outputPath = Path.GetFileNameWithoutExtension(testDocPath) + "_math_function_test.docx";
                    doc.Save(outputPath);
                    Console.WriteLine($"\n处理后的文档已保存为: {outputPath}");
                }
                else
                {
                    Console.WriteLine("\n❌ 处理失败！");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"详细错误信息: {ex.StackTrace}");
            }

            Console.WriteLine("\n=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 分析文档中的数学公式状态
        /// </summary>
        /// <param name="doc">文档对象</param>
        /// <param name="stage">分析阶段（处理前/处理后）</param>
        private static void AnalyzeMathFormulas(Document doc, string stage)
        {
            // 获取所有数学公式节点
            NodeCollection mathNodes = doc.GetChildNodes(NodeType.OfficeMath, true);

            Console.WriteLine($"{stage}共找到 {mathNodes.Count} 个数学公式");

            int formulaIndex = 0;
            foreach (OfficeMath mathNode in mathNodes)
            {
                formulaIndex++;
                string formulaText = mathNode.ToString(SaveFormat.Text).Trim();

                // 只分析包含目标函数的公式
                if (ContainsTargetFunctions(formulaText))
                {
                    Console.WriteLine($"\n--- 公式 {formulaIndex}: {formulaText} ---");

                    // 分析Run结构
                    NodeCollection runs = mathNode.GetChildNodes(NodeType.Run, true);
                    Console.WriteLine($"Run数量: {runs.Count}");

                    for (int i = 0; i < runs.Count; i++)
                    {
                        Run run = (Run)runs[i];
                        string runText = run.Text ?? "";
                        if (!string.IsNullOrEmpty(runText))
                        {
                            Console.WriteLine($"  Run[{i}]: \"{runText}\" (斜体: {run.Font.Italic})");
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查文本是否包含目标函数
        /// </summary>
        /// <param name="text">要检查的文本</param>
        /// <returns>是否包含目标函数</returns>
        private static bool ContainsTargetFunctions(string text)
        {
            string[] targetFunctions = { "sin", "cos", "tan", "log", "ln", "min", "max" };

            foreach (string func in targetFunctions)
            {
                if (text.Contains(func))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// 创建测试文档
        /// </summary>
        private static void CreateTestDocument()
        {
            try
            {
                // 创建新文档
                Document doc = new Document();
                DocumentBuilder builder = new DocumentBuilder(doc);

                // 添加标题
                builder.Font.Size = 16;
                builder.Font.Bold = true;
                builder.Writeln("数学函数测试文档");
                builder.Font.Bold = false;
                builder.Font.Size = 12;
                builder.Writeln();

                // 添加一些包含数学函数的公式
                builder.Writeln("测试公式1：");
                builder.InsertHtml("f(x) = <i>sin</i>x + <i>cos</i>x");
                builder.Writeln();
                builder.Writeln();

                builder.Writeln("测试公式2：");
                builder.InsertHtml("g(x) = <i>min</i>{<i>sin</i>ωx, <i>cos</i>ωx}");
                builder.Writeln();
                builder.Writeln();

                builder.Writeln("测试公式3：");
                builder.InsertHtml("h(x) = <i>log</i>(<i>tan</i>x) + <i>ln</i>(x)");
                builder.Writeln();
                builder.Writeln();

                builder.Writeln("测试公式4（部分匹配）：");
                builder.InsertHtml("k(x) = a<i>sin</i>nx + b<i>cos</i>mx");
                builder.Writeln();

                // 保存文档
                string outputPath = "TestMathFunctions.docx";
                doc.Save(outputPath);

                Console.WriteLine($"✅ 测试文档创建成功: {outputPath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建文档时发生错误: {ex.Message}");
            }
        }
    }
}
